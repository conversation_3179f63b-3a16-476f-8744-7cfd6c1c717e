<script setup>
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter.js'
import { BaseButton } from '@/components/ui'

const router = useRouter()
const authStore = useAuthStore()

// 导航到角色列表
const goToCharacters = () => {
  router.push('/characters')
}

// 导航到注册
const goToRegister = () => {
  router.push('/register')
}
</script>

<template>
  <div class="home-page">
    <!-- 主要内容区域 - 极简主义，单列居中布局 -->
    <main class="main-content">
      <div class="container">
        <div class="hero-layout center-layout">

          <!-- 主标题 - 大量留白，绝对焦点 -->
          <div class="hero-text space-hero fade-in">
            <h1 class="hero-title text-hero text-primary">温暖树洞</h1>
            <p class="hero-subtitle text-h2 text-secondary mb-lg">聊天交友，情感陪伴</p>
          </div>

          <!-- 核心发光图形 - 屏幕中央的绝对焦点 -->
          <div class="hero-orb space-section slide-up">
            <div class="glow-orb"></div>
          </div>

          <!-- 主要行动按钮 - 明亮的柠檬黄，强烈引导性 -->
          <div class="hero-actions space-component fade-in">
            <BaseButton
              v-if="!authStore.isLoggedIn"
              variant="primary"
              @click="goToRegister"
              class="cta-button"
            >
              发起
            </BaseButton>
            <BaseButton
              v-else
              variant="primary"
              @click="goToCharacters"
              class="cta-button"
            >
              发起
            </BaseButton>
          </div>

          <!-- 次要按钮 - 青色，较低视觉权重 -->
          <div class="secondary-actions fade-in">
            <BaseButton
              variant="secondary"
              @click="goToCharacters"
              class="secondary-button"
            >
              收一收
            </BaseButton>
          </div>

        </div>
      </div>
    </main>

  </div>
</template>

<style scoped>
/* 首页 - 严格按照极简主义设计规范 */
.home-page {
  min-height: 100vh;
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: var(--spacing-4xl) 0;
}

/* 英雄布局 - 单列居中，大量留白 */
.hero-layout {
  width: 100%;
  max-width: var(--content-max-width);
  padding: 0 var(--spacing-lg);
}

/* 主标题区域 - 绝对焦点 */
.hero-text {
  text-align: center;
}

.hero-title {
  margin-bottom: var(--spacing-lg);
  /* 使用渐变文字效果 */
  background: linear-gradient(135deg, var(--color-accent-cyan) 0%, var(--color-accent-yellow) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  margin-bottom: 0;
}

/* 核心发光图形 - 屏幕中央的绝对焦点 */
.hero-orb {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 主要行动按钮 - 明亮的柠檬黄，强烈引导性 */
.hero-actions {
  display: flex;
  justify-content: center;
}

.cta-button {
  min-width: 160px;
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-lg) var(--spacing-3xl);
  min-height: 56px;
}

/* 次要按钮 - 较低视觉权重 */
.secondary-actions {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-xl);
}

.secondary-button {
  min-width: 120px;
  opacity: 0.8;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .main-content {
    padding: var(--spacing-3xl) 0;
  }

  .hero-title {
    font-size: 36px;
  }

  .hero-subtitle {
    font-size: var(--font-size-h3);
  }

  .glow-orb {
    width: 160px;
    height: 160px;
  }

  .cta-button {
    min-width: 140px;
    font-size: var(--font-size-body);
    padding: var(--spacing-md) var(--spacing-xl);
    min-height: 48px;
  }
}

@media (max-width: 480px) {
  .hero-layout {
    padding: 0 var(--spacing-md);
  }

  .hero-title {
    font-size: 28px;
  }

  .glow-orb {
    width: 120px;
    height: 120px;
  }
}
</style>




