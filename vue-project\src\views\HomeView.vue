<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter.js'
import { publicAPI } from '@/api/public.js'
import { BaseButton, BaseCard, BaseAvatar, LoadingSpinner } from '@/components/ui'

const router = useRouter()
const authStore = useAuthStore()

const characters = ref([])
const packages = ref([])
const loading = ref(true)

// 获取数据
onMounted(async () => {
  try {
    const [charactersRes, packagesRes] = await Promise.all([
      publicAPI.getAICharacters({ page: 1, pageSize: 4 }),
      publicAPI.getRechargePackages()
    ])
    characters.value = charactersRes.list
    packages.value = packagesRes
  } catch (error) {
    console.error('Failed to load data:', error)
  } finally {
    loading.value = false
  }
})

// 导航到角色列表
const goToCharacters = () => {
  router.push('/characters')
}

// 导航到聊天
const startChat = (character) => {
  if (authStore.isLoggedIn) {
    router.push(`/chat/${character.id}`)
  } else {
    router.push('/login')
  }
}

// 导航到注册
const goToRegister = () => {
  router.push('/register')
}

// 导航到登录
const goToLogin = () => {
  router.push('/login')
}
</script>

<template>
  <div class="home-page">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <!-- 主标题和描述 -->
          <div class="hero-text fade-in">
            <h1 class="hero-title">温暖树洞</h1>
            <p class="hero-subtitle">聊天交友，情感陪伴</p>
            <p class="hero-description">
              在这里，你可以与AI伙伴分享内心的想法，获得温暖的陪伴和理解。
              无论是工作压力、生活困扰，还是简单的日常分享，我们都在这里倾听。
            </p>
          </div>

          <!-- 中心发光效果 -->
          <div class="hero-glow slide-up">
            <div class="glow-orb"></div>
            <div class="glow-ring"></div>
          </div>

          <!-- 行动按钮 -->
          <div class="hero-actions fade-in">
            <BaseButton
              v-if="!authStore.isLoggedIn"
              variant="primary"
              size="lg"
              @click="goToRegister"
            >
              开始倾诉
            </BaseButton>
            <BaseButton
              v-else
              variant="primary"
              size="lg"
              @click="goToCharacters"
            >
              选择伙伴
            </BaseButton>
            <BaseButton
              variant="secondary"
              size="lg"
              @click="goToCharacters"
            >
              浏览角色
            </BaseButton>
          </div>
        </div>
      </div>
    </section>

    <!-- AI角色展示区域 -->
    <section class="characters-section">
      <div class="container">
        <div class="section-header text-center">
          <h2 class="section-title">精选AI伙伴</h2>
          <p class="section-subtitle">选择最适合你的倾听者</p>
        </div>

        <LoadingSpinner v-if="loading" center text="加载中..." />

        <div v-else class="characters-grid">
          <BaseCard
            v-for="character in characters"
            :key="character.id"
            :hoverable="true"
            :clickable="true"
            class="character-card"
            @click="startChat(character)"
          >
            <div class="character-content">
              <BaseAvatar
                :src="character.avatar_url"
                :name="character.name"
                :alt="character.name"
                size="lg"
                class="character-avatar"
              />
              <div class="character-info">
                <h3 class="character-name">{{ character.name }}</h3>
                <p class="character-description">{{ character.description }}</p>
                <div class="character-stats">
                  <span class="popularity">{{ character.popularity }} 人在聊</span>
                </div>
              </div>
            </div>
          </BaseCard>
        </div>

        <div class="text-center mt-xl">
          <BaseButton variant="ghost" @click="goToCharacters">
            查看更多角色
          </BaseButton>
        </div>
      </div>
    </section>

    <!-- 价格套餐区域 -->
    <section class="pricing-section">
      <div class="container">
        <div class="section-header text-center">
          <h2 class="section-title">充值套餐</h2>
          <p class="section-subtitle">选择适合你的套餐，开始温暖对话</p>
        </div>

        <div class="pricing-grid">
          <BaseCard
            v-for="pkg in packages"
            :key="pkg.id"
            :hoverable="true"
            class="pricing-card"
          >
            <div class="pricing-content">
              <h3 class="pricing-name">{{ pkg.name }}</h3>
              <div class="pricing-price">
                <span class="price-symbol">¥</span>
                <span class="price-amount">{{ pkg.price }}</span>
              </div>
              <div class="pricing-balance">
                获得 {{ Math.floor(pkg.balance_amount / 1000) }}K 余额
              </div>
              <p class="pricing-description">{{ pkg.description }}</p>
              <BaseButton
                variant="primary"
                class="pricing-button"
                @click="authStore.isLoggedIn ? $router.push('/recharge') : goToLogin()"
              >
                立即充值
              </BaseButton>
            </div>
          </BaseCard>
        </div>
      </div>
    </section>

    <!-- CTA区域 -->
    <section class="cta-section">
      <div class="container">
        <div class="cta-content text-center">
          <h2 class="cta-title">准备好开始了吗？</h2>
          <p class="cta-description">
            加入我们，找到属于你的温暖倾听者
          </p>
          <div class="cta-actions">
            <BaseButton
              v-if="!authStore.isLoggedIn"
              variant="primary"
              size="lg"
              @click="goToRegister"
            >
              免费注册
            </BaseButton>
            <BaseButton
              v-if="!authStore.isLoggedIn"
              variant="ghost"
              size="lg"
              @click="goToLogin"
            >
              已有账号
            </BaseButton>
            <BaseButton
              v-else
              variant="primary"
              size="lg"
              @click="goToCharacters"
            >
              开始聊天
            </BaseButton>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
}

/* 英雄区域 */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 800px;
  z-index: 2;
}

.hero-text {
  margin-bottom: var(--spacing-3xl);
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-lg);
}

.hero-description {
  font-size: var(--font-size-base);
  color: var(--color-text-muted);
  line-height: var(--line-height-relaxed);
  max-width: 600px;
}

/* 发光效果 */
.hero-glow {
  position: relative;
  margin: var(--spacing-3xl) 0;
}

.glow-orb {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: var(--gradient-glow);
  position: relative;
  animation: pulse 3s ease-in-out infinite;
}

.glow-ring {
  position: absolute;
  top: -20px;
  left: -20px;
  width: 240px;
  height: 240px;
  border: 2px solid var(--color-accent-cyan);
  border-radius: 50%;
  opacity: 0.3;
  animation: rotate 10s linear infinite;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  justify-content: center;
}

/* 区域样式 */
.characters-section,
.pricing-section,
.cta-section {
  padding: var(--spacing-3xl) 0;
}

.section-header {
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

/* 角色网格 */
.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.character-card {
  cursor: pointer;
}

.character-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.character-avatar {
  margin-bottom: var(--spacing-md);
}

.character-info {
  flex: 1;
}

.character-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.character-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacing-md);
}

.character-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
}

.popularity {
  font-size: var(--font-size-xs);
  color: var(--color-accent-cyan);
  background: rgba(0, 212, 255, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-pill);
}

/* 价格网格 */
.pricing-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  max-width: 1000px;
  margin: 0 auto;
}

.pricing-card {
  text-align: center;
  position: relative;
}

.pricing-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.pricing-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.pricing-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: var(--spacing-sm);
}

.price-symbol {
  font-size: var(--font-size-lg);
  color: var(--color-accent-yellow);
}

.price-amount {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-accent-yellow);
}

.pricing-balance {
  font-size: var(--font-size-base);
  color: var(--color-accent-cyan);
  margin-bottom: var(--spacing-md);
}

.pricing-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  margin-bottom: var(--spacing-lg);
  flex: 1;
}

.pricing-button {
  margin-top: auto;
}

/* CTA区域 */
.cta-section {
  background: var(--color-background-glass);
  backdrop-filter: var(--glass-backdrop);
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.cta-description {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xl);
}

.cta-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

/* 动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .glow-orb {
    width: 150px;
    height: 150px;
  }

  .glow-ring {
    width: 190px;
    height: 190px;
    top: -20px;
    left: -20px;
  }

  .characters-grid {
    grid-template-columns: 1fr;
  }

  .pricing-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: var(--font-size-2xl);
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: var(--spacing-xl) 0;
  }

  .characters-section,
  .pricing-section,
  .cta-section {
    padding: var(--spacing-2xl) 0;
  }
}
</style>
