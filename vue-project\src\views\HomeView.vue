<script setup>
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter.js'
import { BaseButton } from '@/components/ui'

const router = useRouter()
const authStore = useAuthStore()

// 导航到角色列表
const goToCharacters = () => {
  router.push('/characters')
}

// 导航到注册
const goToRegister = () => {
  router.push('/register')
}
</script>

<template>
  <div class="home-app">
    <!-- APP主界面 - 全屏视口，无滚动 -->
    <div class="app-container">

      <!-- 顶部区域 - 品牌标识 -->
      <div class="app-header">
        <h1 class="app-title">温暖树洞</h1>
        <p class="app-subtitle">聊天交友，情感陪伴</p>
      </div>

      <!-- 中心区域 - 核心发光图形 -->
      <div class="app-center">
        <div class="glow-orb"></div>
      </div>

      <!-- 底部区域 - 主要操作按钮 */
      <div class="app-actions">
        <BaseButton
          v-if="!authStore.isLoggedIn"
          variant="primary"
          @click="goToRegister"
          class="primary-action"
        >
          发起
        </BaseButton>
        <BaseButton
          v-else
          variant="primary"
          @click="goToCharacters"
          class="primary-action"
        >
          发起
        </BaseButton>

        <BaseButton
          variant="secondary"
          @click="goToCharacters"
          class="secondary-action"
        >
          收一收
        </BaseButton>
      </div>

    </div>
  </div>
</template>

<style scoped>
/* APP首页 - 全屏视口，无滚动，移动端优先 */
.home-app {
  height: 100vh;
  width: 100vw;
  background: var(--color-background-primary);
  overflow: hidden;
  position: relative;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2xl) var(--spacing-lg);
  box-sizing: border-box;
}

/* 顶部区域 - 品牌标识，占用约20%高度 */
.app-header {
  flex: 0 0 auto;
  text-align: center;
  padding-top: var(--spacing-xl);
}

.app-title {
  font-size: var(--font-size-hero);
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-md);
  background: linear-gradient(135deg, var(--color-accent-cyan) 0%, var(--color-accent-yellow) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: var(--letter-spacing-tight);
}

.app-subtitle {
  font-size: var(--font-size-h2);
  color: var(--color-text-secondary);
  margin: 0;
}

/* 中心区域 - 核心发光图形，占用约50%高度 */
.app-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 底部区域 - 主要操作按钮，占用约30%高度 */
.app-actions {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  width: 100%;
  max-width: 280px;
  padding-bottom: var(--spacing-xl);
}

.primary-action {
  width: 100%;
  min-height: 56px;
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-bold);
  border-radius: var(--border-radius-pill);
}

.secondary-action {
  width: 100%;
  min-height: 48px;
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-pill);
  opacity: 0.9;
}

/* 移动端优化 - 确保在各种屏幕尺寸下都是全屏APP体验 */
@media (max-width: 768px) {
  .app-container {
    padding: var(--spacing-xl) var(--spacing-md);
  }

  .app-title {
    font-size: 36px;
  }

  .app-subtitle {
    font-size: var(--font-size-h3);
  }

  .glow-orb {
    width: 180px;
    height: 180px;
  }

  .app-actions {
    max-width: 260px;
  }
}

@media (max-width: 480px) {
  .app-container {
    padding: var(--spacing-lg) var(--spacing-md);
  }

  .app-title {
    font-size: 32px;
  }

  .app-subtitle {
    font-size: var(--font-size-body);
  }

  .glow-orb {
    width: 150px;
    height: 150px;
  }

  .primary-action {
    min-height: 52px;
    font-size: var(--font-size-body);
  }

  .secondary-action {
    min-height: 44px;
    font-size: var(--font-size-small);
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .app-container {
    padding: var(--spacing-md);
  }

  .glow-orb {
    width: 120px;
    height: 120px;
  }

  .app-actions {
    max-width: 240px;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .app-container {
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    padding: var(--spacing-md);
  }

  .app-header {
    flex: 0 0 auto;
    padding-top: 0;
  }

  .app-center {
    flex: 0 0 auto;
  }

  .app-actions {
    flex: 0 0 auto;
    max-width: 200px;
    padding-bottom: 0;
  }

  .glow-orb {
    width: 100px;
    height: 100px;
  }

  .app-title {
    font-size: 24px;
    margin-bottom: var(--spacing-xs);
  }

  .app-subtitle {
    font-size: var(--font-size-small);
  }
}
</style>




