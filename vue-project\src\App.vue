<script setup>
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores/counter.js'
import { BaseNavbar, BaseButton, BaseAvatar } from '@/components/ui'

const authStore = useAuthStore()

const handleLogout = async () => {
  try {
    await authStore.logout()
  } catch (error) {
    console.error('Logout failed:', error)
  }
}
</script>

<template>
  <div id="app">
    <!-- 导航栏 -->
    <BaseNavbar>
      <template #nav>
        <router-link to="/" class="navbar-item">首页</router-link>
        <router-link to="/characters" class="navbar-item">AI伙伴</router-link>
      </template>

      <template #actions>
        <template v-if="authStore.isLoggedIn">
          <router-link to="/profile" class="navbar-item">
            <BaseAvatar
              :src="authStore.user?.avatar"
              :name="authStore.user?.email || 'User'"
              size="sm"
            />
          </router-link>
          <BaseButton variant="ghost" size="sm" @click="handleLogout">
            退出
          </BaseButton>
        </template>
        <template v-else>
          <router-link to="/login">
            <BaseButton variant="ghost" size="sm">登录</BaseButton>
          </router-link>
          <router-link to="/register">
            <BaseButton variant="primary" size="sm">注册</BaseButton>
          </router-link>
        </template>
      </template>
    </BaseNavbar>

    <!-- 主内容区域 -->
    <main class="main-content">
      <RouterView />
    </main>
  </div>
</template>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
}

.navbar-item {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-normal);
  display: flex;
  align-items: center;
}

.navbar-item:hover,
.navbar-item.router-link-active {
  color: var(--color-accent-cyan);
}
</style>
