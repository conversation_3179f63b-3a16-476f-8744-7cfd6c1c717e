// 用户认证状态管理
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { authAPI } from '@/api/auth.js'

export const useAuthStore = defineStore('auth', () => {
  const user = ref(null)
  const token = ref(localStorage.getItem('access_token'))

  const isLoggedIn = computed(() => !!token.value)

  // 登录
  async function login(credentials) {
    try {
      const response = await authAPI.login(credentials)
      token.value = response.access_token
      user.value = response.user
      localStorage.setItem('access_token', response.access_token)
      return response
    } catch (error) {
      throw error
    }
  }

  // 注册
  async function register(userData) {
    try {
      const response = await authAPI.register(userData)
      token.value = response.access_token
      user.value = response.user
      localStorage.setItem('access_token', response.access_token)
      return response
    } catch (error) {
      throw error
    }
  }

  // 登出
  async function logout() {
    try {
      await authAPI.logout()
    } catch (error) {
      console.error('Logout API failed:', error)
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('access_token')
    }
  }

  // 更新用户信息
  function updateUser(userData) {
    user.value = { ...user.value, ...userData }
  }

  return {
    user,
    token,
    isLoggedIn,
    login,
    register,
    logout,
    updateUser
  }
})
