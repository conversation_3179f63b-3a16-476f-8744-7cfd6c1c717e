-----

### **1. 核心数据实体识别**

根据您的功能清单，我识别出以下核心业务实体，它们将构成我们数据库的基础：

  * **用户 (User):** 系统的核心参与者，包括注册用户和管理员。
  * **AI 角色 (AI Character):** 用户交互的对象，由管理员创建和配置。
  * **聊天会话 (Chat Session):** 记录一个特定用户和一个特定AI角色之间的对话关系。
  * **聊天消息 (Chat Message):** 构成对话的具体内容，包括用户发送和AI回复的消息。
  * **用户资产 (User Wallet):** 尽管可以作为用户表的一个字段，但为了未来的扩展性（如多种代币），将其概念化是重要的。在当前设计中，我们将其简化为用户表中的 `balance` 字段。
  * **余额消费日志 (Consumption Log):** 记录每一次对话导致的余额扣减流水，用于对账和展示消费记录。
  * **充值套餐 (Recharge Package):** 管理员在后台配置的商品，用户通过购买它来充值余额。
  * **充值订单 (Recharge Order):** 用户购买充值套餐时生成的订单记录。
  * **系统配置 (System Config):** 存储全局性的系统参数，如计费比例、新用户福利等。
  * **长期记忆 (Long-term Memory):** 为实现高级“记忆”功能，存储用户与角色对话的关键摘要信息。
  * **角色动态事件 (Character Dynamic Event):** 为AI角色注入的短期、动态的设定或话题。

-----

### **2. 数据表结构设计**

接下来，我将为每个实体设计详细的表结构。

#### **2.1 用户表 (users)**

存储系统所有用户（包括管理员）的基础信息。

| 字段名 | 数据类型 | 默认值 | 约束与索引 | 注释 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | - | `PRIMARY KEY`, `AUTO_INCREMENT` | 唯一用户ID |
| `uid` | `VARCHAR(32)` | - | `UNIQUE`, `NOT NULL` | 公开的用户唯一标识，避免暴露自增主键 |
| `phone_number` | `VARCHAR(20)` | `NULL` | `UNIQUE` | 手机号，可用于登录 |
| `email` | `VARCHAR(128)` | `NULL` | `UNIQUE` | 邮箱，可用于登录 |
| `password_hash` | `VARCHAR(255)` | `NULL` | - | 加密后的密码 |
| `balance` | `DECIMAL(18, 4)` | `0.0000` | `NOT NULL` | 用户余额，使用高精度DECIMAL |
| `role` | `ENUM('user', 'admin')` | `'user'` | `NOT NULL` | 用户角色 |
| `status` | `ENUM('active', 'frozen')` | `'active'` | `NOT NULL` | 账户状态 |
| `created_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP` | `NOT NULL` | 创建时间 |
| `updated_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP` | `NOT NULL ON UPDATE CURRENT_TIMESTAMP` | 最后更新时间 |

#### **2.2 AI角色表 (ai\_characters)**

存储所有AI角色的配置信息。

| 字段名 | 数据类型 | 默认值 | 约束与索引 | 注释 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | - | `PRIMARY KEY`, `AUTO_INCREMENT` | 角色唯一ID |
| `name` | `VARCHAR(50)` | - | `NOT NULL` | 角色名称 |
| `avatar_url` | `VARCHAR(255)` | `NULL` | - | 角色头像URL |
| `description` | `VARCHAR(500)` | `NULL` | - | 角色简介 |
| `system_prompt` | `TEXT` | - | `NOT NULL` | 核心System Prompt |
| `popularity` | `INT UNSIGNED` | `0` | `NOT NULL` | 热度值，可后台调整或定时任务更新 |
| `status` | `ENUM('online', 'offline')`| `'offline'` | `NOT NULL` | 状态（上线/下线）|
| `sort_order` | `INT` | `0` | `NOT NULL` | 显示排序，数值越小越靠前 |
| `created_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP` | `NOT NULL` | 创建时间 |
| `updated_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP` | `NOT NULL ON UPDATE CURRENT_TIMESTAMP`| 最后更新时间 |

#### **2.3 聊天会话表 (chat\_sessions)**

标识用户与AI角色的一次完整对话，聚合所有消息。

| 字段名 | 数据类型 | 默认值 | 约束与索引 | 注释 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | - | `PRIMARY KEY`, `AUTO_INCREMENT`| 会话唯一ID |
| `user_id` | `BIGINT UNSIGNED` | - | `FOREIGN KEY` -\> `users(id)` | 用户ID |
| `character_id` | `BIGINT UNSIGNED` | - | `FOREIGN KEY` -\> `ai_characters(id)`| AI角色ID |
| `created_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP` | `NOT NULL` | 会话开始时间 |
| `updated_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP` | `NOT NULL ON UPDATE CURRENT_TIMESTAMP`| 会话最后活跃时间 |
| - | - | - | `UNIQUE KEY (user_id, character_id)` | 确保用户和角色间只有唯一会话 |

#### **2.4 聊天消息表 (chat\_messages)**

存储每一次具体的对话消息。

| 字段名 | 数据类型 | 默认值 | 约束与索引 | 注释 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | - | `PRIMARY KEY`, `AUTO_INCREMENT`| 消息唯一ID |
| `session_id` | `BIGINT UNSIGNED` | - | `FOREIGN KEY` -\> `chat_sessions(id)` | 所属会话ID |
| `sender_type` | `ENUM('user', 'ai')`| - | `NOT NULL` | 发送方类型 |
| `content` | `TEXT` | - | `NOT NULL` | 消息内容 |
| `prompt_tokens` | `INT UNSIGNED` | `0` | `NOT NULL` | API消耗的Prompt Tokens |
| `completion_tokens`| `INT UNSIGNED` | `0` | `NOT NULL` | API消耗的Completion Tokens |
| `total_tokens` | `INT UNSIGNED` | `0` | `NOT NULL` | 总消耗Tokens |
| `created_at` | `TIMESTAMP(3)` | `CURRENT_TIMESTAMP(3)`| `NOT NULL` | 创建时间，精确到毫秒 |

#### **2.5 余额消费日志表 (consumption\_logs)**

记录所有余额变动，主要是对话消耗。

| 字段名 | 数据类型 | 默认值 | 约束与索引 | 注释 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | - | `PRIMARY KEY`, `AUTO_INCREMENT` | 日志ID |
| `user_id` | `BIGINT UNSIGNED` | - | `FOREIGN KEY` -\> `users(id)` | 用户ID |
| `message_id` | `BIGINT UNSIGNED`| `NULL` | `FOREIGN KEY` -\> `chat_messages(id)`| 关联的聊天消息ID |
| `balance_change` | `DECIMAL(18, 4)` | - | `NOT NULL` | 余额变动值（消耗为负数）|
| `balance_after` | `DECIMAL(18, 4)` | - | `NOT NULL` | 变动后的余额，用于对账 |
| `tokens_consumed`| `INT UNSIGNED` | - | `NOT NULL` | 本次消耗的Token数 |
| `description` | `VARCHAR(255)` | `NULL` | - | 变动描述，如“与XX的对话” |
| `created_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP`| `NOT NULL` | 记录时间 |

#### **2.6 充值套餐表 (recharge\_packages)**

后台配置的充值商品。

| 字段名 | 数据类型 | 默认值 | 约束与索引 | 注释 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `INT UNSIGNED` | - | `PRIMARY KEY`, `AUTO_INCREMENT` | 套餐ID |
| `name` | `VARCHAR(100)` | - | `NOT NULL` | 套餐名称，如“尝鲜包” |
| `description` | `VARCHAR(255)` | `NULL` | - | 套餐描述，如“赠送20%” |
| `price` | `DECIMAL(10, 2)` | - | `NOT NULL` | 售价（人民币）|
| `balance_amount` | `DECIMAL(18, 4)` | - | `NOT NULL` | 可获得的余额数量 |
| `status` | `ENUM('active', 'inactive')`| `'inactive'`| `NOT NULL` | 状态（激活/未激活）|
| `sort_order` | `INT` | `0` | `NOT NULL` | 显示排序 |
| `created_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP`| `NOT NULL` | 创建时间 |
| `updated_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP`| `NOT NULL ON UPDATE CURRENT_TIMESTAMP`| 更新时间 |

#### **2.7 充值订单表 (recharge\_orders)**

用户的充值订单记录。

| 字段名 | 数据类型 | 默认值 | 约束与索引 | 注释 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `BIGINT UNSIGNED` | - | `PRIMARY KEY`, `AUTO_INCREMENT` | 订单ID |
| `order_sn` | `VARCHAR(64)` | - | `UNIQUE`, `NOT NULL` | 系统内部订单号 |
| `user_id` | `BIGINT UNSIGNED` | - | `FOREIGN KEY` -\> `users(id)` | 用户ID |
| `package_id` | `INT UNSIGNED` | - | `FOREIGN KEY` -\> `recharge_packages(id)`| 购买的套餐ID |
| `amount_paid` | `DECIMAL(10, 2)` | - | `NOT NULL` | 实际支付金额 |
| `balance_granted`| `DECIMAL(18, 4)` | - | `NOT NULL` | 订单授予的余额 |
| `payment_method` | `VARCHAR(50)` | `NULL` | - | 支付方式，如`alipay`, `wechat_pay`|
| `external_txn_id`| `VARCHAR(128)` | `NULL` | `INDEX` | 支付方返回的交易流水号 |
| `status` | `ENUM('pending', 'completed', 'failed', 'cancelled')` | `'pending'` | `NOT NULL` | 订单状态 |
| `created_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP`| `NOT NULL` | 创建时间 |
| `paid_at` | `TIMESTAMP` | `NULL` | - | 支付成功时间 |
| `updated_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP`| `NOT NULL ON UPDATE CURRENT_TIMESTAMP`| 更新时间 |

#### **2.8 系统配置表 (system\_configs)**

灵活的键值对形式存储全局配置。

| 字段名 | 数据类型 | 默认值 | 约束与索引 | 注释 |
| :--- | :--- | :--- | :--- | :--- |
| `id` | `INT UNSIGNED` | - | `PRIMARY KEY`, `AUTO_INCREMENT` | 配置ID |
| `config_key` | `VARCHAR(100)` | - | `UNIQUE`, `NOT NULL` | 配置键 |
| `config_value` | `TEXT` | `NULL` | - | 配置值 |
| `description` | `VARCHAR(255)` | `NULL` | - | 配置项说明 |
| `updated_at` | `TIMESTAMP` | `CURRENT_TIMESTAMP`| `NOT NULL ON UPDATE CURRENT_TIMESTAMP`| 更新时间 |

-----

### **3. 表关系说明**

  * `users` 与 `recharge_orders`: 一对多。一个用户可以有多个充值订单。
  * `users` 与 `chat_sessions`: 一对多。一个用户可以与多个AI角色建立聊天会话。
  * `ai_characters` 与 `chat_sessions`: 一对多。一个AI角色可以与多个用户建立聊天会话。
  * `chat_sessions` 与 `chat_messages`: 一对多。一个会话包含多条聊天消息。
  * `users` 与 `consumption_logs`: 一对多。一个用户有多条消费记录。
  * `recharge_packages` 与 `recharge_orders`: 一对多。一个套餐可以被多个订单购买。
  * `chat_messages` 与 `consumption_logs`: 一对一（可空）。一条AI回复消息对应一条消费记录。

-----

### **4. 完整 CREATE TABLE SQL 语句**

以下是可直接在MySQL中执行的完整SQL代码：

```sql
-- 数据库字符集推荐使用 utf8mb4 以支持emoji
-- SET NAMES utf8mb4;

-- ----------------------------
-- Table structure for users
-- ----------------------------
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` VARCHAR(32) NOT NULL COMMENT '公开的用户唯一标识',
  `phone_number` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
  `email` VARCHAR(128) DEFAULT NULL COMMENT '邮箱',
  `password_hash` VARCHAR(255) DEFAULT NULL COMMENT '加密后的密码',
  `balance` DECIMAL(18, 4) NOT NULL DEFAULT '0.0000' COMMENT '用户余额',
  `role` ENUM('user', 'admin') NOT NULL DEFAULT 'user' COMMENT '用户角色: user-普通用户, admin-管理员',
  `status` ENUM('active', 'frozen') NOT NULL DEFAULT 'active' COMMENT '账户状态: active-正常, frozen-冻结',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uid` (`uid`),
  UNIQUE KEY `uk_phone_number` (`phone_number`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- Table structure for ai_characters
-- ----------------------------
CREATE TABLE `ai_characters` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` VARCHAR(50) NOT NULL COMMENT '角色名称',
  `avatar_url` VARCHAR(255) DEFAULT NULL COMMENT '角色头像URL',
  `description` VARCHAR(500) DEFAULT NULL COMMENT '角色简介',
  `system_prompt` TEXT NOT NULL COMMENT '核心System Prompt',
  `popularity` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '热度值',
  `status` ENUM('online', 'offline') NOT NULL DEFAULT 'offline' COMMENT '状态: online-上线, offline-下线',
  `sort_order` INT NOT NULL DEFAULT '0' COMMENT '显示排序，值越小越靠前',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI角色表';

-- ----------------------------
-- Table structure for chat_sessions
-- ----------------------------
CREATE TABLE `chat_sessions` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `character_id` BIGINT UNSIGNED NOT NULL COMMENT 'AI角色ID',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '会话创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '会话最后活跃时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_character` (`user_id`, `character_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_character_id` (`character_id`),
  CONSTRAINT `fk_chatsessions_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_chatsessions_character` FOREIGN KEY (`character_id`) REFERENCES `ai_characters` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- ----------------------------
-- Table structure for chat_messages
-- ----------------------------
CREATE TABLE `chat_messages` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `session_id` BIGINT UNSIGNED NOT NULL COMMENT '所属会话ID',
  `sender_type` ENUM('user', 'ai') NOT NULL COMMENT '发送方类型: user-用户, ai-AI角色',
  `content` TEXT NOT NULL COMMENT '消息内容',
  `prompt_tokens` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT 'API消耗的Prompt Tokens',
  `completion_tokens` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT 'API消耗的Completion Tokens',
  `total_tokens` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '总消耗Tokens',
  `created_at` TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间(精确到毫秒)',
  PRIMARY KEY (`id`),
  KEY `idx_session_id_created_at` (`session_id`, `created_at`),
  CONSTRAINT `fk_chatmessages_session` FOREIGN KEY (`session_id`) REFERENCES `chat_sessions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- ----------------------------
-- Table structure for consumption_logs
-- ----------------------------
CREATE TABLE `consumption_logs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `message_id` BIGINT UNSIGNED DEFAULT NULL COMMENT '关联的聊天消息ID(AI回复的那条)',
  `balance_change` DECIMAL(18, 4) NOT NULL COMMENT '余额变动值(消耗为负)',
  `balance_after` DECIMAL(18, 4) NOT NULL COMMENT '变动后余额',
  `tokens_consumed` INT UNSIGNED NOT NULL COMMENT '本次消耗的Token数',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '变动描述',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id_created_at` (`user_id`, `created_at`),
  KEY `idx_message_id` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='余额消费日志表';

-- ----------------------------
-- Table structure for recharge_packages
-- ----------------------------
CREATE TABLE `recharge_packages` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` VARCHAR(100) NOT NULL COMMENT '套餐名称',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '套餐描述',
  `price` DECIMAL(10, 2) NOT NULL COMMENT '售价(元)',
  `balance_amount` DECIMAL(18, 4) NOT NULL COMMENT '可获得的余额数量',
  `status` ENUM('active', 'inactive') NOT NULL DEFAULT 'inactive' COMMENT '状态: active-激活, inactive-未激活',
  `sort_order` INT NOT NULL DEFAULT '0' COMMENT '显示排序',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值套餐表';

-- ----------------------------
-- Table structure for recharge_orders
-- ----------------------------
CREATE TABLE `recharge_orders` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_sn` VARCHAR(64) NOT NULL COMMENT '系统内部订单号',
  `user_id` BIGINT UNSIGNED NOT NULL COMMENT '用户ID',
  `package_id` INT UNSIGNED NOT NULL COMMENT '购买的套餐ID',
  `amount_paid` DECIMAL(10, 2) NOT NULL COMMENT '实际支付金额',
  `balance_granted` DECIMAL(18, 4) NOT NULL COMMENT '订单授予的余额',
  `payment_method` VARCHAR(50) DEFAULT NULL COMMENT '支付方式',
  `external_txn_id` VARCHAR(128) DEFAULT NULL COMMENT '支付方返回的交易流水号',
  `status` ENUM('pending', 'completed', 'failed', 'cancelled') NOT NULL DEFAULT 'pending' COMMENT '订单状态',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `paid_at` TIMESTAMP NULL DEFAULT NULL COMMENT '支付成功时间',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_sn` (`order_sn`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_external_txn_id` (`external_txn_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值订单表';

-- ----------------------------
-- Table structure for system_configs
-- ----------------------------
CREATE TABLE `system_configs` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT COMMENT '配置值',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '配置项说明',
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';
```

-----

### **5. 性能优化与索引建议**

除了主键、外键和唯一约束自动创建的索引外，我还额外设计了以下关键索引以提升查询性能：

1.  **`chat_messages` 表**

      * **索引:** `KEY idx_session_id_created_at (session_id, created_at)`
      * **原因:** 这是最高频的查询场景。当用户打开对话窗口加载历史消息时，总是会根据 `session_id` 筛选，并按 `created_at`（时间）排序。这个联合索引能极大地提升历史消息的加载速度。

2.  **`consumption_logs` 表**

      * **索引:** `KEY idx_user_id_created_at (user_id, created_at)`
      * **原因:** 用户在个人中心查看自己的“消费记录”时，会按用户ID筛选并按时间倒序排列。此索引完全覆盖该查询，避免全表扫描。

3.  **`recharge_orders` 表**

      * **索引:** `KEY idx_user_id (user_id)`
      * **原因:** 快速查询指定用户的全部充值订单。
      * **索引:** `KEY idx_external_txn_id (external_txn_id)`
      * **原因:** 在处理支付回调时，需要根据支付方返回的交易号快速定位到我们的内部订单，此索引至关重要。

4.  **`users` 表**

      * **索引:** `UNIQUE KEY uk_phone_number (phone_number)` 和 `UNIQUE KEY uk_email (email)`
      * **原因:** 这两个字段是登录凭证，`UNIQUE`约束保证了其唯一性，并自动创建索引，能极大加速登录验证时的查询速度。
