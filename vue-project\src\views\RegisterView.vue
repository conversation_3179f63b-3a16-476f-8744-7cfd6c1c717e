<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-card glass-card">
        <div class="register-header">
          <h1 class="register-title">加入我们</h1>
          <p class="register-subtitle">创建你的温暖树洞账户</p>
        </div>

        <form @submit.prevent="handleRegister" class="register-form">
          <BaseInput
            v-model="form.email"
            type="email"
            label="邮箱"
            placeholder="请输入邮箱地址"
            required
            :error="errors.email"
            @blur="validateField('email')"
          />

          <BaseInput
            v-model="form.password"
            type="password"
            label="密码"
            placeholder="请输入密码（至少6位）"
            required
            :error="errors.password"
            @blur="validateField('password')"
          />

          <BaseInput
            v-model="form.confirmPassword"
            type="password"
            label="确认密码"
            placeholder="请再次输入密码"
            required
            :error="errors.confirmPassword"
            @blur="validateField('confirmPassword')"
          />

          <div class="form-actions">
            <BaseButton
              type="submit"
              variant="primary"
              size="lg"
              :loading="loading"
              :disabled="!isFormValid"
              class="register-button"
            >
              注册
            </BaseButton>
          </div>

          <div class="form-footer">
            <p class="login-link">
              已有账户？
              <router-link to="/login" class="link">立即登录</router-link>
            </p>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter.js'
import { BaseInput, BaseButton } from '@/components/ui'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)

const form = reactive({
  email: '',
  password: '',
  confirmPassword: ''
})

const errors = reactive({
  email: '',
  password: '',
  confirmPassword: ''
})

// 表单验证
const validateField = (field) => {
  switch (field) {
    case 'email':
      if (!form.email) {
        errors.email = '请输入邮箱地址'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
        errors.email = '请输入有效的邮箱地址'
      } else {
        errors.email = ''
      }
      break
    case 'password':
      if (!form.password) {
        errors.password = '请输入密码'
      } else if (form.password.length < 6) {
        errors.password = '密码至少需要6位字符'
      } else {
        errors.password = ''
      }
      // 如果确认密码已填写，重新验证确认密码
      if (form.confirmPassword) {
        validateField('confirmPassword')
      }
      break
    case 'confirmPassword':
      if (!form.confirmPassword) {
        errors.confirmPassword = '请确认密码'
      } else if (form.confirmPassword !== form.password) {
        errors.confirmPassword = '两次输入的密码不一致'
      } else {
        errors.confirmPassword = ''
      }
      break
  }
}

const validateForm = () => {
  validateField('email')
  validateField('password')
  validateField('confirmPassword')
}

const isFormValid = computed(() => {
  return form.email && 
         form.password && 
         form.confirmPassword &&
         !errors.email && 
         !errors.password && 
         !errors.confirmPassword
})

// 处理注册
const handleRegister = async () => {
  validateForm()
  
  if (!isFormValid.value) {
    return
  }

  try {
    loading.value = true
    await authStore.register({
      email: form.email,
      password: form.password
    })
    
    // 注册成功，跳转到首页
    router.push('/')
  } catch (error) {
    // 显示错误信息
    if (error.message.includes('邮箱已注册')) {
      errors.email = '该邮箱已被注册'
    } else {
      errors.email = error.message || '注册失败，请重试'
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.register-container {
  width: 100%;
  max-width: 400px;
}

.register-card {
  padding: var(--spacing-3xl);
}

.register-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.register-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.register-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-actions {
  margin-top: var(--spacing-md);
}

.register-button {
  width: 100%;
}

.form-footer {
  text-align: center;
  margin-top: var(--spacing-lg);
}

.login-link {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.link {
  color: var(--color-accent-cyan);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.link:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .register-page {
    padding: var(--spacing-md);
  }
  
  .register-card {
    padding: var(--spacing-xl);
  }
  
  .register-title {
    font-size: var(--font-size-2xl);
  }
}
</style>
