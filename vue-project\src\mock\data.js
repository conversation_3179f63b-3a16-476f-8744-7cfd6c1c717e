// Mock 数据
export const mockAICharacters = [
  {
    id: 1,
    name: "温柔的倾听者",
    avatar_url: "https://via.placeholder.com/100x100/4F46E5/FFFFFF?text=温柔",
    description: "我是一个温柔耐心的倾听者，愿意陪伴你度过每一个需要倾诉的时刻。",
    system_prompt: "你是一个温柔、耐心、善解人意的倾听者...",
    popularity: 1234
  },
  {
    id: 2,
    name: "毒舌但直率的朋友",
    avatar_url: "https://via.placeholder.com/100x100/EF4444/FFFFFF?text=直率",
    description: "虽然我说话直接，但我会给你最真实的建议和支持。",
    system_prompt: "你是一个说话直接但关心朋友的角色...",
    popularity: 987
  },
  {
    id: 3,
    name: "智慧的心理导师",
    avatar_url: "https://via.placeholder.com/100x100/10B981/FFFFFF?text=智慧",
    description: "我具备专业的心理学知识，能够帮助你分析问题，找到解决方案。",
    system_prompt: "你是一个具备心理学专业知识的导师...",
    popularity: 2156
  },
  {
    id: 4,
    name: "活泼的小伙伴",
    avatar_url: "https://via.placeholder.com/100x100/F59E0B/FFFFFF?text=活泼",
    description: "我总是充满活力，能够带给你快乐和正能量！",
    system_prompt: "你是一个活泼开朗、充满正能量的小伙伴...",
    popularity: 756
  }
]

export const mockRechargePackages = [
  {
    id: 1,
    name: "尝鲜包",
    price: "6.00",
    balance_amount: "6000.0000",
    description: "适合初次体验的用户"
  },
  {
    id: 2,
    name: "标准包",
    price: "30.00",
    balance_amount: "35000.0000",
    description: "性价比最高，赠送5000余额"
  },
  {
    id: 3,
    name: "畅聊包",
    price: "68.00",
    balance_amount: "85000.0000",
    description: "长期使用，赠送17000余额"
  },
  {
    id: 4,
    name: "VIP包",
    price: "128.00",
    balance_amount: "180000.0000",
    description: "超值套餐，赠送52000余额"
  }
]

export const mockUser = {
  uid: "u-d290f1ee",
  phone_number: "138****1234",
  email: "<EMAIL>",
  balance: "87.5000",
  created_at: "2024-01-15T10:30:00Z"
}

export const mockMessages = [
  {
    id: 101,
    sender_type: "user",
    content: "你好，我最近工作压力很大，感觉很焦虑",
    created_at: "2024-01-20T14:30:00Z"
  },
  {
    id: 102,
    sender_type: "ai",
    content: "我能理解你的感受。工作压力确实会让人感到焦虑，这是很正常的反应。你愿意和我分享一下具体是什么让你感到压力吗？",
    created_at: "2024-01-20T14:30:15Z"
  },
  {
    id: 103,
    sender_type: "user",
    content: "主要是项目deadline很紧，而且老板的要求越来越高",
    created_at: "2024-01-20T14:32:00Z"
  },
  {
    id: 104,
    sender_type: "ai",
    content: "听起来你确实面临着不小的挑战。紧迫的截止日期和高标准的要求会让任何人都感到压力。你有尝试过一些缓解压力的方法吗？比如合理安排时间，或者和同事、上级沟通？",
    created_at: "2024-01-20T14:32:20Z"
  }
]

export const mockSessions = [
  {
    character: {
      id: 1,
      name: "温柔的倾听者",
      avatar_url: "https://via.placeholder.com/100x100/4F46E5/FFFFFF?text=温柔"
    },
    last_message: "听起来你确实面临着不小的挑战...",
    updated_at: "2024-01-20T14:32:20Z"
  },
  {
    character: {
      id: 3,
      name: "智慧的心理导师",
      avatar_url: "https://via.placeholder.com/100x100/10B981/FFFFFF?text=智慧"
    },
    last_message: "从心理学角度来看，你的反应是正常的...",
    updated_at: "2024-01-19T16:45:00Z"
  }
]

export const mockConsumptionLogs = [
  {
    description: "与'温柔的倾听者'的对话",
    balance_change: "-2.5000",
    balance_after: "87.5000",
    created_at: "2024-01-20T14:32:20Z"
  },
  {
    description: "与'智慧的心理导师'的对话",
    balance_change: "-3.2000",
    balance_after: "90.0000",
    created_at: "2024-01-19T16:45:00Z"
  }
]

export const mockRechargeOrders = [
  {
    order_sn: "RO20240120001",
    amount_paid: "30.00",
    balance_granted: "35000.0000",
    status: "completed",
    created_at: "2024-01-20T10:00:00Z"
  },
  {
    order_sn: "RO20240115001",
    amount_paid: "6.00",
    balance_granted: "6000.0000",
    status: "completed",
    created_at: "2024-01-15T15:30:00Z"
  }
]
