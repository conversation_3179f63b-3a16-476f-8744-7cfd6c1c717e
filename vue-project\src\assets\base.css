/* 树洞应用设计系统 - CSS 变量定义 */
:root {
  /* 颜色系统 */
  --color-primary-dark: #000000;
  --color-primary-blue: #1a1a2e;
  --color-background-main: linear-gradient(135deg, #0f3460 0%, #16213e 50%, #0e1a2e 100%);
  --color-background-card: rgba(255, 255, 255, 0.05);
  --color-background-glass: rgba(255, 255, 255, 0.08);

  /* 文字颜色 */
  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.8);
  --color-text-muted: rgba(255, 255, 255, 0.6);

  /* 强调色 */
  --color-accent-cyan: #00d4ff;
  --color-accent-yellow: #ffd700;
  --color-accent-green: #10b981;
  --color-accent-red: #ef4444;

  /* 渐变色 */
  --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #ffd700 100%);
  --gradient-glow: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, rgba(0, 212, 255, 0.1) 70%);
  --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

  /* 间距系统 (8px 基础单位) */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;

  /* 字体系统 */
  --font-family-main: 'PingFang SC', 'Source Han Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  --font-size-4xl: 40px;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;

  /* 圆角系统 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 12px;
  --border-radius-xl: 16px;
  --border-radius-pill: 9999px;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-glow-cyan: 0 0 20px rgba(0, 212, 255, 0.3);
  --shadow-glow-yellow: 0 0 20px rgba(255, 215, 0, 0.3);

  /* 层级系统 */
  --z-index-dropdown: 1000;
  --z-index-modal: 1050;
  --z-index-tooltip: 1100;
  --z-index-toast: 1200;

  /* 过渡动画 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.6s ease-out;

  /* 玻璃拟态效果 */
  --glass-backdrop: blur(10px);
  --glass-border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 全局重置样式 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  min-height: 100vh;
  font-family: var(--font-family-main);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background: var(--color-primary-dark);
  background-image: var(--color-background-main);
  background-attachment: fixed;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: var(--color-accent-cyan);
  border-radius: var(--border-radius-pill);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-yellow);
}

/* 选择文本样式 */
::selection {
  background: var(--color-accent-cyan);
  color: var(--color-primary-dark);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--color-accent-cyan);
  outline-offset: 2px;
}
