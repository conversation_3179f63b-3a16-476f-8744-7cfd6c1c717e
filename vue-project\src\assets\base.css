/* 树洞应用设计系统 - 严格按照UI设计规范 */
:root {
  /* 主色调 - 深邃的暗夜蓝/纯黑背景 */
  --color-background-primary: #000000;
  --color-background-secondary: #0a0a0f;
  --color-background-tertiary: #1a1a2e;

  /* 文字颜色 - 高亮度纯白文字确保强烈对比 */
  --color-text-primary: #ffffff;
  --color-text-secondary: rgba(255, 255, 255, 0.9);
  --color-text-muted: rgba(255, 255, 255, 0.7);
  --color-text-dark: #000000; /* 用于黄色按钮上的深色文字 */

  /* 强调色 - 明亮的荧光感青色和充满活力的柠檬黄 */
  --color-accent-cyan: #00ffff; /* 荧光青色 */
  --color-accent-yellow: #ffff00; /* 柠檬黄 */
  --color-accent-cyan-soft: #00d4ff; /* 稍微柔和的青色用于次要元素 */

  /* 玻璃拟态效果 - 半透明背景 */
  --color-glass-bg: rgba(255, 255, 255, 0.05);
  --color-glass-border: rgba(255, 255, 255, 0.1);
  --color-glass-bg-hover: rgba(255, 255, 255, 0.08);

  /* 渐变 - 从青绿到蓝紫色的柔和渐变 */
  --gradient-background: linear-gradient(135deg, #001122 0%, #002244 50%, #000011 100%);
  --gradient-glow-orb: radial-gradient(circle, #ffff00 0%, #00ffff 70%, transparent 100%);
  --gradient-button-primary: linear-gradient(135deg, #ffff00 0%, #ffdd00 100%);
  --gradient-button-secondary: linear-gradient(135deg, #00ffff 0%, #00d4ff 100%);

  /* 间距系统 - 严格8px基础单位 */
  --spacing-unit: 8px;
  --spacing-xs: calc(var(--spacing-unit) * 0.5); /* 4px */
  --spacing-sm: var(--spacing-unit); /* 8px */
  --spacing-md: calc(var(--spacing-unit) * 2); /* 16px */
  --spacing-lg: calc(var(--spacing-unit) * 3); /* 24px */
  --spacing-xl: calc(var(--spacing-unit) * 4); /* 32px */
  --spacing-2xl: calc(var(--spacing-unit) * 6); /* 48px */
  --spacing-3xl: calc(var(--spacing-unit) * 8); /* 64px */
  --spacing-4xl: calc(var(--spacing-unit) * 12); /* 96px */
  --spacing-5xl: calc(var(--spacing-unit) * 16); /* 128px */

  /* 字体系统 - 现代无衬线中文字体 */
  --font-family-primary: 'PingFang SC', 'Source Han Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', sans-serif;

  /* 字号层级 - 明确的层级关系 */
  --font-size-hero: 48px; /* 大标题如"温暖树洞" */
  --font-size-h1: 32px; /* 中等标题 */
  --font-size-h2: 24px;
  --font-size-h3: 20px;
  --font-size-body: 16px; /* 正文 */
  --font-size-small: 14px; /* 辅助信息 */
  --font-size-button: 16px; /* 按钮文字 */
  --font-size-caption: 12px;

  /* 字重 - 清晰的视觉重量对比 */
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 - 1.5左右确保可读性 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.6;

  /* 字间距 - 标题负间距，正文默认 */
  --letter-spacing-tight: -0.01em;
  --letter-spacing-normal: 0;

  /* 圆角 - 柔和的圆角 */
  --border-radius-sm: 8px;
  --border-radius-md: 16px;
  --border-radius-lg: 24px;
  --border-radius-pill: 9999px; /* 胶囊形状 */

  /* 阴影和发光效果 */
  --shadow-glow-cyan: 0 0 30px rgba(0, 255, 255, 0.3);
  --shadow-glow-yellow: 0 0 30px rgba(255, 255, 0, 0.3);
  --shadow-glow-orb: 0 0 60px rgba(255, 255, 0, 0.4), 0 0 120px rgba(0, 255, 255, 0.2);
  --shadow-button-hover: 0 4px 20px rgba(255, 255, 0, 0.4);
  --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3);

  /* 模糊效果 */
  --blur-glass: blur(20px);

  /* 过渡动画 - 60fps流畅体验 */
  --transition-fast: 0.15s cubic-bezier(0.25, 0.1, 0.25, 1);
  --transition-normal: 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
  --transition-slow: 0.6s cubic-bezier(0.25, 0.1, 0.25, 1);

  /* 层级 */
  --z-index-background: -1;
  --z-index-content: 1;
  --z-index-glass: 10;
  --z-index-modal: 1000;
  --z-index-tooltip: 1100;

  /* 内容宽度限制 - 85-90%屏幕宽度 */
  --content-width-mobile: 90%;
  --content-width-tablet: 85%;
  --content-width-desktop: 80%;
  --content-max-width: 600px; /* 移动端优先的最大宽度 */
}

/* 全局重置样式 */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  height: 100%;
}

body {
  min-height: 100vh;
  height: 100%;
  font-family: var(--font-family-primary);
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background: var(--color-background-primary);
  /* 深邃的暗夜背景，不使用复杂渐变 */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

/* 滚动条样式 - 极简设计 */
::-webkit-scrollbar {
  width: 2px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--color-accent-cyan);
  border-radius: var(--border-radius-pill);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-yellow);
}

/* 选择文本样式 */
::selection {
  background: var(--color-accent-cyan);
  color: var(--color-text-dark);
}

/* 焦点样式 - 荧光青色发光 */
:focus-visible {
  outline: none;
  box-shadow: 0 0 0 2px var(--color-accent-cyan);
}
