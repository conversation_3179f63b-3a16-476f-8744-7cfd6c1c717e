// 认证相关 API
import api, { USE_MOCK, createMockWrapper, mockAPI } from './index.js'

export const authAPI = {
  // 用户注册
  register: USE_MOCK
    ? createMockWrapper(mockAPI.auth.register)
    : (data) => api.post('/auth/register', data),

  // 用户登录
  login: USE_MOCK
    ? createMockWrapper(mockAPI.auth.login)
    : (data) => api.post('/auth/login', data),

  // 用户登出
  logout: USE_MOCK
    ? createMockWrapper(mockAPI.auth.logout)
    : () => api.post('/auth/logout')
}
