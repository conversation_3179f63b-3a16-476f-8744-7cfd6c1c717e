这份清单将全面覆盖电脑端网页和手机端网页的需求，并基于 `Vue3 + Node.js + MySQL` 技术栈进行规划，确保技术可行性。

---

### **项目核心理念分析**

* **核心业务模式:** 用户通过付费充值获得“余额”（或称为“代币”、“能量”），在与不同设定的AI角色聊天时，根据对话消耗的Tokens（由API返回）扣除相应余额，为用户提供一个私密、安全的情感宣泄和陪伴渠道。
* **目标用户群体:**
    * 内心有烦恼、压力，但不想或不便向身边人倾诉的年轻人。
    * 希望获得无条件倾听、理解和积极反馈的用户。
    * 对AI、虚拟角色互动感兴趣，寻求新奇体验的用户。
    * 需要特定角色（如心理伙伴、专业顾问、虚拟恋人等）进行对话模拟的用户。

---

### **用户角色与权限体系**

1.  **游客 (未登录用户)**
    * **权限:**
        * 可以浏览网站介绍、AI角色列表及简介。
        * 可以查看价格体系和充值套餐。
        * 可以体验与某个“体验角色”进行有限次数（或token额度）的免费对话。
        * 不能与正式AI角色对话，没有独立的聊天记录和余额。
2.  **注册用户 (登录用户)**
    * **权限:**
        * 拥有游客所有权限。
        * 拥有独立的账户和余额系统。
        * 可以充值购买余额。
        * 可以与所有上线的AI角色进行不限次的对话（只要余额充足）。
        * 可以查看和管理自己的聊天记录。
        * 可以管理个人账户信息（修改密码、绑定邮箱等）。
3.  **管理员 (后台运营人员)**
    * **权限:**
        * 拥有后台管理系统的访问权限。
        * 管理用户账户（查看、冻结等）。
        * 管理AI角色（创建、编辑、上下线角色，配置Prompt）。
        * 管理订单和财务（查看充值记录、统计收入）。
        * 配置系统参数（如Token与余额的兑换比例、新用户赠送额度等）。
        * 查看全站运营数据分析。

---

### **功能需求清单 (Markdown)**

#### **第一部分：前台功能 (用户端 - 电脑/手机网页自适应)**

**模块一：核心对话模块 (Chat)**

* **1.1 AI角色选择**
    * **描述:** 以卡片或列表形式展示所有可对话的AI角色。每个角色有独立的头像、名称、简介（如“温柔的倾听者”、“毒舌但直率的朋友”等）和当前热度（如多少人正在聊）。
    * **流程:** 用户浏览 -> 点击感兴趣的角色卡片 -> 进入该角色的专属对话界面。
    * **权限:** 游客可浏览，注册用户可选择并对话。

* **1.2 对话界面**
    * **描述:** 经典IM（即时通讯）布局。上方显示当前对话角色的信息。中间是对话内容区，下方是文本输入框和发送按钮。
    * **交互:**
        * 支持回车键或点击按钮发送消息。
        * 对话实时滚动，异步加载历史消息。
        * 在等待AI回复时，显示“对方正在输入中...”的动效，提升真实感。
        * 发送消息后，前端立即扣除预估的Token费用（或等待后端返回后扣费），并实时更新余额显示。若余额不足，输入框置灰或弹出提示。
    * **权限:** 注册用户专享（游客可体验受限的试用版）。

* **1.3 余额消耗提醒**
    * **描述:** 在对话界面显著位置实时显示用户余额。当余额低于某个阈值（如10%）时，余额数字变为警告色（如橙色或红色），并出现一个不打扰对话的小提示，引导用户充值。
    * **体验:** 避免因余额突然用尽而中断深度对话，影响用户体验。

**模块二：用户账户模块 (User Center)**

* **2.1 注册与登录**
    * **描述:** 提供手机号+验证码或邮箱+密码的注册/登录方式。登录状态通过Token（JWT）维持。
    * **流程:** 输入手机/邮箱 -> 获取验证码/输入密码 -> 验证成功 -> 登录完成并跳转。
    * **亮点:** 注册成功后可自动赠送少量体验余额，引导用户立即体验核心功能。

* **2.2 个人中心**
    * **描述:** 聚合用户所有相关信息。
    * **内容:**
        * **我的余额:** 显眼位置展示当前余额，并放置“去充值”按钮。
        * **消费记录:** 以列表形式展示每一次对话消耗的余额详情（对话角色、消耗Token数、消耗时间）。
        * **充值记录:** 以列表形式展示所有历史充值订单（充值金额、支付方式、订单状态、时间）。
        * **聊天记录:** 提供与不同AI角色的历史会话列表，点击可进入具体的对话界面回顾内容。支持按角色筛选和关键词搜索。
        * **账户设置:** 修改密码、换绑手机/邮箱等。

* **2.3 余额与充值系统**
    * **描述:** 余额系统是项目的商业核心。
    * **功能:**
        * **套餐配置:** 后台可灵活配置多种充值套餐，如“6元/6000余额”、“30元/35000余额”等，价格越高质量越优惠。
        * **支付集成:** 对接主流支付渠道（如微信支付、支付宝）。用户选择套餐 -> 扫码/跳转支付 -> 支付成功后回调 -> 系统自动为用户账户增加相应余额。
        * **计费逻辑:**
            * **定义兑换率:** 在后台设定 `1元 = N余额` 的兑换率。
            * **定义消耗率:** 在后台设定 `1余额 = M个Token` 的消耗率。这个比例是核心，需要根据API成本和运营策略来精细调整。
            * **扣费:** 用户每发送一次消息，调用AI API，API返回消耗的`prompt_tokens` + `completion_tokens`总数，后端根据消耗率计算需扣除的余额，并更新用户数据库。

**模块三：内容与展示模块 (Content)**

* **3.1 首页 (Landing Page)**
    * **描述:** 吸引新用户的门户。
    * **内容:** 项目Slogan、核心功能简介、精选AI角色展示、用户评价（虚拟或真实）、价格套餐、引导注册/登录的醒目按钮（Call To Action）。
    * **设计:** 界面要简洁、温馨，符合“树洞”的定位。

* **3.2 角色详情页 (可选，初期可简化)**
    * **描述:** 点击角色列表，不直接进入对话，而是先进入一个介绍页。
    * **内容:** 角色的高清大图、更详细的背景故事和性格设定（Prompt的一部分可以公开）、擅长聊的话题标签、其他用户的评价等，增加角色的立体感和吸引力。

---

#### **第二部分：后台管理系统 (Admin Panel)**

**模块四：管理与配置**

* **4.1 仪表盘 (Dashboard)**
    * **描述:** 核心运营数据一览。
    * **数据指标:** 今日新增用户、活跃用户数、今日充值总额、今日Token消耗量、当前在线人数等。

* **4.2 用户管理**
    * **描述:** 管理所有注册用户。
    * **功能:** 用户列表（支持按手机号/UID搜索）、查看用户详情（余额、聊天记录、订单）、手动修改余额（用于补偿或活动）、冻结/解冻账户。

* **4.3 AI角色管理**
    * **描述:** 项目的灵魂所在，必须灵活可配。
    * **功能:**
        * **创建/编辑角色:** 上传角色头像、填写名称、简介。
        * **核心Prompt配置:** 一个大的文本域，用于输入该角色的核心`System Prompt`。这是决定角色行为和性格的关键。
        * **状态管理:** 控制角色的“上线”、“下线”状态。下线后用户端列表不可见。
        * **排序/推荐:** 可设置角色在前端的显示顺序，或设置“推荐”标签。

* **4.4 财务管理**
    * **描述:** 掌握收入情况。
    * **功能:** 充值订单列表（可按时间、状态筛选）、财务统计报表（日报、月报）、退款处理（预留接口）。

* **4.5 系统设置**
    * **描述:** 全局参数配置。
    * **功能:**
        * **计费模型设置:** 调整Token与余额的兑换比例。
        * **新用户福利:** 设置新用户注册赠送的余额数量。
        * **支付配置:** 填写微信/支付宝支付的API密钥等信息。
        * **API Key池:** 可配置多个AI API Key，实现负载均衡或备用切换。

---

### **💡 亮点功能与扩展建议**

1.  **记忆与长期关系 (Memory Feature)**
    * **描述:** 这是提升用户粘性的王牌功能。除了单次会话的上下文记忆，可以为每个用户和每个角色的组合，建立一个独立的长期记忆数据库（例如，用向量数据库存取关键信息）。
    * **实现:** AI在对话中可以定期总结对话要点（“用户最近在为工作烦恼”、“用户的宠物叫咪咪”），并存入长期记忆。下次对话开始时，将这些记忆要点作为背景信息注入到Prompt中。
    * **价值:** AI会“记得”用户，营造出独一无二的专属伙伴关系，极大提升用户的情感依赖和付费意愿。

2.  **动态事件与角色更新 (Dynamic Events)**
    * **描述:** 让AI角色“活”起来。可以由管理员定期为某些角色“注入”新的短期经历或话题。
    * **示例:** 在后台为一个叫“小晴”的角色增加一条新设定：“我今天去看了最新上映的电影《XX》，感觉真不错！” 当用户和小晴聊天时，她可能会主动提起这个话题，让对话更自然、更有新鲜感。
    * **价值:** 打破AI对话的静态感，让用户有持续探索的欲望，提高用户活跃度和复访率。
