// 用户中心相关 API
import api, { USE_MOCK, createMockWrapper, mockAPI } from './index.js'

export const userAPI = {
  // 获取当前用户信息
  getProfile: USE_MOCK
    ? createMockWrapper(mockAPI.user.getProfile)
    : () => api.get('/user/profile'),

  // 修改密码
  changePassword: USE_MOCK
    ? createMockWrapper(mockAPI.user.changePassword)
    : (data) => api.put('/user/password', data),

  // 获取消费记录
  getConsumptionLogs: USE_MOCK
    ? createMockWrapper(mockAPI.user.getConsumptionLogs)
    : (params = {}) => api.get('/user/consumption-logs', { params }),

  // 获取充值订单记录
  getRechargeOrders: USE_MOCK
    ? createMockWrapper(mockAPI.user.getRechargeOrders)
    : (params = {}) => api.get('/user/recharge-orders', { params })
}
