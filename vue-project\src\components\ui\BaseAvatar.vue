<template>
  <div :class="avatarClasses">
    <img
      v-if="src"
      :src="src"
      :alt="alt"
      @error="handleImageError"
    />
    <div v-else class="avatar-fallback">
      <slot>
        {{ fallbackText }}
      </slot>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'

const props = defineProps({
  src: {
    type: String,
    default: ''
  },
  alt: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg', 'xl'].includes(value)
  },
  name: {
    type: String,
    default: ''
  }
})

const imageError = ref(false)

const avatarClasses = computed(() => [
  'avatar',
  `avatar-${props.size}`
])

const fallbackText = computed(() => {
  if (props.name) {
    return props.name.charAt(0).toUpperCase()
  }
  return '?'
})

const handleImageError = () => {
  imageError.value = true
}
</script>

<style scoped>
.avatar-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gradient-primary);
  color: var(--color-primary-dark);
  font-weight: var(--font-weight-semibold);
}
</style>
