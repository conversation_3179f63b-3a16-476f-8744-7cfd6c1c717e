<template>
  <div class="login-page">
    <!-- 返回按钮 -->
    <BaseButton variant="ghost" @click="$router.go(-1)" class="back-button">
      ← 返回
    </BaseButton>

    <div class="login-container">
      <div class="login-card glass-card">
        <div class="login-header">
          <h1 class="login-title">欢迎回来</h1>
          <p class="login-subtitle">登录你的温暖树洞账户</p>
        </div>

        <form @submit.prevent="handleLogin" class="login-form">
          <BaseInput
            v-model="form.identity"
            type="email"
            label="邮箱"
            placeholder="请输入邮箱地址"
            required
            :error="errors.identity"
            @blur="validateField('identity')"
          />

          <BaseInput
            v-model="form.password"
            type="password"
            label="密码"
            placeholder="请输入密码"
            required
            :error="errors.password"
            @blur="validateField('password')"
          />

          <div class="form-actions">
            <BaseButton
              type="submit"
              variant="primary"
              size="lg"
              :loading="loading"
              :disabled="!isFormValid"
              class="login-button"
            >
              登录
            </BaseButton>
          </div>

          <div class="form-footer">
            <p class="register-link">
              还没有账户？
              <router-link to="/register" class="link">立即注册</router-link>
            </p>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter.js'
import { BaseInput, BaseButton } from '@/components/ui'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)

const form = reactive({
  identity: '',
  password: ''
})

const errors = reactive({
  identity: '',
  password: ''
})

// 表单验证
const validateField = (field) => {
  switch (field) {
    case 'identity':
      if (!form.identity) {
        errors.identity = '请输入邮箱地址'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.identity)) {
        errors.identity = '请输入有效的邮箱地址'
      } else {
        errors.identity = ''
      }
      break
    case 'password':
      if (!form.password) {
        errors.password = '请输入密码'
      } else if (form.password.length < 6) {
        errors.password = '密码至少需要6位字符'
      } else {
        errors.password = ''
      }
      break
  }
}

const validateForm = () => {
  validateField('identity')
  validateField('password')
}

const isFormValid = computed(() => {
  return form.identity && 
         form.password && 
         !errors.identity && 
         !errors.password
})

// 处理登录
const handleLogin = async () => {
  validateForm()
  
  if (!isFormValid.value) {
    return
  }

  try {
    loading.value = true
    await authStore.login({
      identity: form.identity,
      password: form.password
    })
    
    // 登录成功，跳转到首页
    router.push('/')
  } catch (error) {
    // 显示错误信息
    if (error.message.includes('用户名或密码错误')) {
      errors.password = '用户名或密码错误'
    } else {
      errors.password = error.message || '登录失败，请重试'
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 登录APP页面 - 全屏视口 */
.login-page {
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  background: var(--color-background-primary);
  overflow: hidden;
  position: relative;
}

.back-button {
  position: absolute;
  top: var(--spacing-lg);
  left: var(--spacing-lg);
  z-index: 10;
  min-width: auto;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-small);
}

.login-container {
  width: 100%;
  max-width: 400px;
}

.login-card {
  padding: var(--spacing-3xl);
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.login-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.form-actions {
  margin-top: var(--spacing-md);
}

.login-button {
  width: 100%;
}

.form-footer {
  text-align: center;
  margin-top: var(--spacing-lg);
}

.register-link {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.link {
  color: var(--color-accent-cyan);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.link:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .login-page {
    padding: var(--spacing-md);
  }
  
  .login-card {
    padding: var(--spacing-xl);
  }
  
  .login-title {
    font-size: var(--font-size-2xl);
  }
}
</style>
