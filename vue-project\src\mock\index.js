// Mock 服务 - 模拟后端 API 响应
import { 
  mockAICharacters, 
  mockRechargePackages, 
  mockUser, 
  mockMessages, 
  mockSessions,
  mockConsumptionLogs,
  mockRechargeOrders
} from './data.js'

// 模拟网络延迟
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms))

// 模拟成功响应格式
const successResponse = (data) => ({
  code: 0,
  message: "Success",
  data
})

// 模拟错误响应格式
const errorResponse = (code, message) => ({
  code,
  message,
  data: null
})

export const mockAPI = {
  // 认证相关
  auth: {
    async login(credentials) {
      await delay()
      if (credentials.identity === '<EMAIL>' && credentials.password === '123456') {
        return successResponse({
          access_token: 'mock_jwt_token_' + Date.now(),
          user: mockUser
        })
      } else {
        throw new Error('用户名或密码错误')
      }
    },

    async register(userData) {
      await delay()
      // 简单验证
      if (!userData.email || !userData.password) {
        throw new Error('邮箱和密码不能为空')
      }
      return successResponse({
        access_token: 'mock_jwt_token_' + Date.now(),
        user: { ...mockUser, email: userData.email }
      })
    },

    async logout() {
      await delay(200)
      return successResponse(null)
    }
  },

  // 公开接口
  public: {
    async getAICharacters(params = {}) {
      await delay()
      const { page = 1, pageSize = 10 } = params
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = mockAICharacters.slice(start, end)
      
      return successResponse({
        list,
        total: mockAICharacters.length
      })
    },

    async getAICharacter(id) {
      await delay()
      const character = mockAICharacters.find(c => c.id === parseInt(id))
      if (character) {
        return successResponse(character)
      } else {
        throw new Error('角色不存在')
      }
    },

    async getRechargePackages() {
      await delay()
      return successResponse(mockRechargePackages)
    }
  },

  // 聊天相关
  chat: {
    async sendMessage(data) {
      await delay(1000) // 模拟AI响应时间
      const aiReplies = [
        "我理解你的感受，请继续告诉我更多细节。",
        "这确实是一个值得思考的问题，你觉得什么方法可能会有帮助？",
        "听起来你已经很努力了，不要太苛责自己。",
        "从另一个角度来看，这也许是一个成长的机会。",
        "我一直在这里倾听，你可以放心地表达你的想法。"
      ]
      
      const randomReply = aiReplies[Math.floor(Math.random() * aiReplies.length)]
      
      return successResponse({
        reply_message: {
          sender_type: "ai",
          content: randomReply
        },
        balance_change: "-2.5000",
        current_balance: "85.0000"
      })
    },

    async getMessages(characterId, params = {}) {
      await delay()
      return successResponse({
        messages: mockMessages
      })
    },

    async getSessions(params = {}) {
      await delay()
      const { page = 1, pageSize = 10 } = params
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = mockSessions.slice(start, end)
      
      return successResponse({
        list,
        total: mockSessions.length
      })
    }
  },

  // 用户相关
  user: {
    async getProfile() {
      await delay()
      return successResponse(mockUser)
    },

    async changePassword(data) {
      await delay()
      if (data.current_password === '123456' && data.new_password) {
        return successResponse(null)
      } else {
        throw new Error('当前密码错误')
      }
    },

    async getConsumptionLogs(params = {}) {
      await delay()
      const { page = 1, pageSize = 10 } = params
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = mockConsumptionLogs.slice(start, end)
      
      return successResponse({
        list,
        total: mockConsumptionLogs.length
      })
    },

    async getRechargeOrders(params = {}) {
      await delay()
      const { page = 1, pageSize = 10 } = params
      const start = (page - 1) * pageSize
      const end = start + pageSize
      const list = mockRechargeOrders.slice(start, end)
      
      return successResponse({
        list,
        total: mockRechargeOrders.length
      })
    }
  },

  // 支付相关
  payment: {
    async createOrder(data) {
      await delay()
      return successResponse({
        order_sn: 'RO' + Date.now(),
        pay_url: 'https://mock-payment-url.com/pay'
      })
    },

    async getOrderStatus(orderSn) {
      await delay()
      return successResponse({
        order_sn: orderSn,
        status: 'completed'
      })
    }
  }
}
