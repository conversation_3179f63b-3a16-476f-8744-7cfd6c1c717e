<template>
  <div class="profile-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="profile-header">
        <h1 class="page-title">个人中心</h1>
      </div>

      <LoadingSpinner v-if="loading" center text="加载中..." />

      <div v-else class="profile-content">
        <!-- 用户信息卡片 -->
        <BaseCard class="user-info-card">
          <div class="user-info">
            <BaseAvatar
              :src="userStore.profile?.avatar"
              :name="userStore.profile?.email"
              size="xl"
              class="user-avatar"
            />
            <div class="user-details">
              <h2 class="user-name">{{ userStore.profile?.email || '用户' }}</h2>
              <p class="user-id">ID: {{ userStore.profile?.uid }}</p>
              <p class="join-date">
                加入时间: {{ formatDate(userStore.profile?.created_at) }}
              </p>
            </div>
          </div>
          
          <div class="balance-section">
            <div class="balance-display">
              <span class="balance-label">当前余额</span>
              <span class="balance-amount">{{ userStore.balance }}</span>
            </div>
            <BaseButton 
              variant="primary" 
              @click="$router.push('/recharge')"
              class="recharge-button"
            >
              充值
            </BaseButton>
          </div>
        </BaseCard>

        <!-- 功能菜单 -->
        <div class="menu-grid">
          <BaseCard 
            :clickable="true" 
            class="menu-item"
            @click="activeTab = 'consumption'"
          >
            <div class="menu-content">
              <div class="menu-icon">💰</div>
              <h3 class="menu-title">消费记录</h3>
              <p class="menu-desc">查看聊天消费明细</p>
            </div>
          </BaseCard>

          <BaseCard 
            :clickable="true" 
            class="menu-item"
            @click="activeTab = 'recharge'"
          >
            <div class="menu-content">
              <div class="menu-icon">💳</div>
              <h3 class="menu-title">充值记录</h3>
              <p class="menu-desc">查看充值订单历史</p>
            </div>
          </BaseCard>

          <BaseCard 
            :clickable="true" 
            class="menu-item"
            @click="activeTab = 'sessions'"
          >
            <div class="menu-content">
              <div class="menu-icon">💬</div>
              <h3 class="menu-title">聊天记录</h3>
              <p class="menu-desc">查看历史对话</p>
            </div>
          </BaseCard>

          <BaseCard 
            :clickable="true" 
            class="menu-item"
            @click="activeTab = 'settings'"
          >
            <div class="menu-content">
              <div class="menu-icon">⚙️</div>
              <h3 class="menu-title">账户设置</h3>
              <p class="menu-desc">修改密码等设置</p>
            </div>
          </BaseCard>
        </div>

        <!-- 详细内容区域 -->
        <div v-if="activeTab" class="detail-section">
          <!-- 消费记录 -->
          <BaseCard v-if="activeTab === 'consumption'" class="detail-card">
            <template #header>
              <h3>消费记录</h3>
            </template>
            
            <div v-if="consumptionLoading" class="loading-container">
              <LoadingSpinner text="加载消费记录..." />
            </div>
            
            <div v-else-if="userStore.consumptionLogs.length === 0" class="empty-state">
              <p>暂无消费记录</p>
            </div>
            
            <div v-else class="records-list">
              <div 
                v-for="log in userStore.consumptionLogs" 
                :key="log.created_at"
                class="record-item"
              >
                <div class="record-info">
                  <p class="record-desc">{{ log.description }}</p>
                  <p class="record-time">{{ formatDateTime(log.created_at) }}</p>
                </div>
                <div class="record-amount negative">
                  {{ log.balance_change }}
                </div>
              </div>
            </div>
          </BaseCard>

          <!-- 充值记录 -->
          <BaseCard v-if="activeTab === 'recharge'" class="detail-card">
            <template #header>
              <h3>充值记录</h3>
            </template>
            
            <div v-if="rechargeLoading" class="loading-container">
              <LoadingSpinner text="加载充值记录..." />
            </div>
            
            <div v-else-if="userStore.rechargeOrders.length === 0" class="empty-state">
              <p>暂无充值记录</p>
            </div>
            
            <div v-else class="records-list">
              <div 
                v-for="order in userStore.rechargeOrders" 
                :key="order.order_sn"
                class="record-item"
              >
                <div class="record-info">
                  <p class="record-desc">充值订单 {{ order.order_sn }}</p>
                  <p class="record-time">{{ formatDateTime(order.created_at) }}</p>
                </div>
                <div class="record-amount positive">
                  +{{ order.balance_granted }}
                </div>
              </div>
            </div>
          </BaseCard>

          <!-- 聊天记录 -->
          <BaseCard v-if="activeTab === 'sessions'" class="detail-card">
            <template #header>
              <h3>聊天记录</h3>
            </template>
            
            <div v-if="sessionsLoading" class="loading-container">
              <LoadingSpinner text="加载聊天记录..." />
            </div>
            
            <div v-else-if="chatStore.sessions.length === 0" class="empty-state">
              <p>暂无聊天记录</p>
            </div>
            
            <div v-else class="sessions-list">
              <div 
                v-for="session in chatStore.sessions" 
                :key="session.character.id"
                class="session-item"
                @click="$router.push(`/chat/${session.character.id}`)"
              >
                <BaseAvatar
                  :src="session.character.avatar_url"
                  :name="session.character.name"
                  size="md"
                  class="session-avatar"
                />
                <div class="session-info">
                  <h4 class="session-name">{{ session.character.name }}</h4>
                  <p class="session-last-message">{{ session.last_message }}</p>
                  <p class="session-time">{{ formatDateTime(session.updated_at) }}</p>
                </div>
              </div>
            </div>
          </BaseCard>

          <!-- 账户设置 -->
          <BaseCard v-if="activeTab === 'settings'" class="detail-card">
            <template #header>
              <h3>账户设置</h3>
            </template>
            
            <form @submit.prevent="changePassword" class="settings-form">
              <BaseInput
                v-model="passwordForm.currentPassword"
                type="password"
                label="当前密码"
                placeholder="请输入当前密码"
                required
                :error="passwordErrors.currentPassword"
              />

              <BaseInput
                v-model="passwordForm.newPassword"
                type="password"
                label="新密码"
                placeholder="请输入新密码（至少6位）"
                required
                :error="passwordErrors.newPassword"
              />

              <BaseInput
                v-model="passwordForm.confirmPassword"
                type="password"
                label="确认新密码"
                placeholder="请再次输入新密码"
                required
                :error="passwordErrors.confirmPassword"
              />

              <BaseButton
                type="submit"
                variant="primary"
                :loading="passwordLoading"
                :disabled="!isPasswordFormValid"
              >
                修改密码
              </BaseButton>
            </form>
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive, watch } from 'vue'
import { useUserStore } from '@/stores/user.js'
import { useChatStore } from '@/stores/chat.js'
import { BaseCard, BaseAvatar, BaseButton, BaseInput, LoadingSpinner } from '@/components/ui'

const userStore = useUserStore()
const chatStore = useChatStore()

const loading = ref(true)
const activeTab = ref('')
const consumptionLoading = ref(false)
const rechargeLoading = ref(false)
const sessionsLoading = ref(false)
const passwordLoading = ref(false)

// 密码修改表单
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const passwordErrors = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN')
}

const formatDateTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN')
}

// 验证密码表单
const validatePasswordForm = () => {
  passwordErrors.currentPassword = ''
  passwordErrors.newPassword = ''
  passwordErrors.confirmPassword = ''

  if (!passwordForm.currentPassword) {
    passwordErrors.currentPassword = '请输入当前密码'
  }

  if (!passwordForm.newPassword) {
    passwordErrors.newPassword = '请输入新密码'
  } else if (passwordForm.newPassword.length < 6) {
    passwordErrors.newPassword = '密码至少需要6位字符'
  }

  if (!passwordForm.confirmPassword) {
    passwordErrors.confirmPassword = '请确认新密码'
  } else if (passwordForm.confirmPassword !== passwordForm.newPassword) {
    passwordErrors.confirmPassword = '两次输入的密码不一致'
  }
}

const isPasswordFormValid = computed(() => {
  return passwordForm.currentPassword &&
         passwordForm.newPassword &&
         passwordForm.confirmPassword &&
         !passwordErrors.currentPassword &&
         !passwordErrors.newPassword &&
         !passwordErrors.confirmPassword
})

// 修改密码
const changePassword = async () => {
  validatePasswordForm()

  if (!isPasswordFormValid.value) {
    return
  }

  try {
    passwordLoading.value = true
    await userStore.changePassword({
      current_password: passwordForm.currentPassword,
      new_password: passwordForm.newPassword
    })

    // 重置表单
    passwordForm.currentPassword = ''
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''

    alert('密码修改成功')
  } catch (error) {
    passwordErrors.currentPassword = error.message || '密码修改失败'
  } finally {
    passwordLoading.value = false
  }
}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    await userStore.fetchProfile()
  } catch (error) {
    console.error('Failed to load profile:', error)
  } finally {
    loading.value = false
  }
}

// 监听 activeTab 变化，加载对应数据
watch(activeTab, async (newTab) => {
  if (newTab === 'consumption' && userStore.consumptionLogs.length === 0) {
    try {
      consumptionLoading.value = true
      await userStore.fetchConsumptionLogs({ page: 1, pageSize: 20 })
    } catch (error) {
      console.error('Failed to load consumption logs:', error)
    } finally {
      consumptionLoading.value = false
    }
  } else if (newTab === 'recharge' && userStore.rechargeOrders.length === 0) {
    try {
      rechargeLoading.value = true
      await userStore.fetchRechargeOrders({ page: 1, pageSize: 20 })
    } catch (error) {
      console.error('Failed to load recharge orders:', error)
    } finally {
      rechargeLoading.value = false
    }
  } else if (newTab === 'sessions' && chatStore.sessions.length === 0) {
    try {
      sessionsLoading.value = true
      await chatStore.loadSessions({ page: 1, pageSize: 20 })
    } catch (error) {
      console.error('Failed to load sessions:', error)
    } finally {
      sessionsLoading.value = false
    }
  }
})

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  padding-top: 80px; /* 为导航栏留出空间 */
  padding-bottom: var(--spacing-3xl);
}

.profile-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.profile-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

/* 用户信息卡片 */
.user-info-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
}

.user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.user-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.user-id,
.join-date {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

.balance-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.balance-display {
  text-align: center;
}

.balance-label {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.balance-amount {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-accent-yellow);
}

/* 功能菜单网格 */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.menu-item {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.menu-content {
  text-align: center;
}

.menu-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-md);
}

.menu-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.menu-desc {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* 详细内容区域 */
.detail-section {
  margin-top: var(--spacing-xl);
}

.detail-card {
  max-width: 800px;
  margin: 0 auto;
}

.loading-container {
  padding: var(--spacing-xl);
  text-align: center;
}

.empty-state {
  padding: var(--spacing-xl);
  text-align: center;
  color: var(--color-text-muted);
}

/* 记录列表 */
.records-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--color-background-glass);
  border-radius: var(--border-radius-md);
  border: var(--glass-border);
}

.record-info {
  flex: 1;
}

.record-desc {
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.record-time {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  margin: 0;
}

.record-amount {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.record-amount.positive {
  color: var(--color-accent-green);
}

.record-amount.negative {
  color: var(--color-accent-red);
}

/* 会话列表 */
.sessions-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.session-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--color-background-glass);
  border-radius: var(--border-radius-md);
  border: var(--glass-border);
  cursor: pointer;
  transition: all var(--transition-normal);
}

.session-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.session-info {
  flex: 1;
}

.session-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.session-last-message {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  margin: 0;
}

/* 设置表单 */
.settings-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  max-width: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-info-card {
    flex-direction: column;
    text-align: center;
  }

  .user-info {
    flex-direction: column;
    text-align: center;
  }

  .menu-grid {
    grid-template-columns: 1fr;
  }

  .record-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .session-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
}

@media (max-width: 480px) {
  .profile-page {
    padding-top: 70px;
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .balance-amount {
    font-size: var(--font-size-xl);
  }
}
</style>
