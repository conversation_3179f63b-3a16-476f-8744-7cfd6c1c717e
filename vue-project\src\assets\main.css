@import './base.css';
@import './components.css';

/* 应用主容器 - APP形式，固定视口 */
#app {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  background: var(--color-background-primary);
  overflow: hidden; /* 禁止滚动 */
  position: fixed; /* 固定定位，防止地址栏影响 */
  top: 0;
  left: 0;
}

/* 容器系统 - 移动端优先，单列居中布局 */
.container {
  width: var(--content-width-mobile);
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* 平板和桌面端容器调整 */
@media (min-width: 768px) {
  .container {
    width: var(--content-width-tablet);
  }
}

@media (min-width: 1024px) {
  .container {
    width: var(--content-width-desktop);
  }
}

/* 布局工具类 - 基于Flexbox的单列居中布局 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

/* 严格中心对齐 */
.center-layout {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

/* 间距工具类 - 严格8px网格系统 */
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }
.mt-2xl { margin-top: var(--spacing-2xl); }
.mt-3xl { margin-top: var(--spacing-3xl); }
.mt-4xl { margin-top: var(--spacing-4xl); }
.mt-5xl { margin-top: var(--spacing-5xl); }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }
.mb-2xl { margin-bottom: var(--spacing-2xl); }
.mb-3xl { margin-bottom: var(--spacing-3xl); }
.mb-4xl { margin-bottom: var(--spacing-4xl); }
.mb-5xl { margin-bottom: var(--spacing-5xl); }

/* 大量留白的间距类 */
.space-hero { margin: var(--spacing-5xl) 0; }
.space-section { margin: var(--spacing-4xl) 0; }
.space-component { margin: var(--spacing-3xl) 0; }

/* 文字工具类 - 明确的层级关系 */
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-dark { color: var(--color-text-dark); }
.text-cyan { color: var(--color-accent-cyan); }
.text-yellow { color: var(--color-accent-yellow); }

/* 字号层级 */
.text-hero {
  font-size: var(--font-size-hero);
  font-weight: var(--font-weight-bold);
  letter-spacing: var(--letter-spacing-tight);
  line-height: var(--line-height-tight);
}
.text-h1 {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--letter-spacing-tight);
}
.text-h2 {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-semibold);
}
.text-h3 {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
}
.text-body {
  font-size: var(--font-size-body);
  font-weight: var(--font-weight-regular);
}
.text-small {
  font-size: var(--font-size-small);
  font-weight: var(--font-weight-regular);
}
.text-caption {
  font-size: var(--font-size-caption);
  font-weight: var(--font-weight-regular);
}

/* 玻璃拟态卡片 - 半透明背景，微妙内发光 */
.glass-card {
  background: var(--color-glass-bg);
  backdrop-filter: var(--blur-glass);
  border: 1px solid var(--color-glass-border);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-glass);
  transition: all var(--transition-normal);
}

.glass-card:hover {
  background: var(--color-glass-bg-hover);
  transform: translateY(-2px);
}

/* 核心发光图形 - 绝对焦点 */
.glow-orb {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: var(--gradient-glow-orb);
  box-shadow: var(--shadow-glow-orb);
  animation: pulse-glow 3s ease-in-out infinite;
  position: relative;
}

.glow-orb::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border: 2px solid var(--color-accent-cyan);
  border-radius: 50%;
  opacity: 0.3;
  animation: rotate-ring 10s linear infinite;
}

/* 发光效果 */
.glow-cyan {
  box-shadow: var(--shadow-glow-cyan);
}

.glow-yellow {
  box-shadow: var(--shadow-glow-yellow);
}

/* 微妙的动画效果 - 60fps流畅体验 */
.fade-in {
  animation: fadeIn 0.6s var(--transition-normal);
}

.slide-up {
  animation: slideUp 0.6s var(--transition-normal);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

@keyframes rotate-ring {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式断点 */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  .text-3xl {
    font-size: var(--font-size-2xl);
  }

  .text-2xl {
    font-size: var(--font-size-xl);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-xl);
  }
}
