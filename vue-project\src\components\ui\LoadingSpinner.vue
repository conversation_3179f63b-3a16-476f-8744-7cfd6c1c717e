<template>
  <div :class="spinnerClasses">
    <div class="loading"></div>
    <span v-if="text" class="loading-text">{{ text }}</span>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  text: {
    type: String,
    default: ''
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  center: {
    type: Boolean,
    default: false
  }
})

const spinnerClasses = computed(() => [
  'loading-container',
  `loading-${props.size}`,
  {
    'loading-center': props.center
  }
])
</script>

<style scoped>
.loading-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.loading-center {
  justify-content: center;
  width: 100%;
  padding: var(--spacing-xl);
}

.loading-text {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

.loading-sm .loading {
  width: 16px;
  height: 16px;
}

.loading-lg .loading {
  width: 32px;
  height: 32px;
}
</style>
