<template>
  <div class="recharge-page">
    <div class="container">
      <!-- 页面头部 -->
      <div class="recharge-header">
        <h1 class="page-title">充值中心</h1>
        <p class="page-subtitle">选择合适的套餐，开始温暖对话</p>
        
        <!-- 当前余额显示 -->
        <div class="current-balance">
          <span class="balance-label">当前余额:</span>
          <span class="balance-amount">{{ userStore.balance }}</span>
        </div>
      </div>

      <LoadingSpinner v-if="loading" center text="加载套餐中..." />

      <div v-else class="recharge-content">
        <!-- 套餐选择 -->
        <div class="packages-section">
          <h2 class="section-title">选择充值套餐</h2>
          
          <div class="packages-grid">
            <BaseCard
              v-for="pkg in packages"
              :key="pkg.id"
              :class="['package-card', { 'selected': selectedPackage?.id === pkg.id }]"
              :clickable="true"
              @click="selectPackage(pkg)"
            >
              <div class="package-content">
                <h3 class="package-name">{{ pkg.name }}</h3>
                <div class="package-price">
                  <span class="price-symbol">¥</span>
                  <span class="price-amount">{{ pkg.price }}</span>
                </div>
                <div class="package-balance">
                  获得 {{ Math.floor(pkg.balance_amount / 1000) }}K 余额
                </div>
                <p class="package-description">{{ pkg.description }}</p>
                
                <!-- 优惠标识 -->
                <div v-if="getDiscountPercent(pkg) > 0" class="discount-badge">
                  超值 {{ getDiscountPercent(pkg) }}%
                </div>
              </div>
            </BaseCard>
          </div>
        </div>

        <!-- 支付方式选择 -->
        <div v-if="selectedPackage" class="payment-section">
          <h2 class="section-title">选择支付方式</h2>
          
          <div class="payment-methods">
            <div
              v-for="method in paymentMethods"
              :key="method.id"
              :class="['payment-method', { 'selected': selectedPayment === method.id }]"
              @click="selectedPayment = method.id"
            >
              <div class="method-icon">{{ method.icon }}</div>
              <div class="method-info">
                <h4 class="method-name">{{ method.name }}</h4>
                <p class="method-desc">{{ method.description }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 订单确认 -->
        <div v-if="selectedPackage && selectedPayment" class="order-section">
          <BaseCard class="order-summary">
            <h3 class="order-title">订单确认</h3>
            
            <div class="order-details">
              <div class="order-item">
                <span class="item-label">套餐:</span>
                <span class="item-value">{{ selectedPackage.name }}</span>
              </div>
              <div class="order-item">
                <span class="item-label">支付金额:</span>
                <span class="item-value price">¥{{ selectedPackage.price }}</span>
              </div>
              <div class="order-item">
                <span class="item-label">获得余额:</span>
                <span class="item-value balance">{{ Math.floor(selectedPackage.balance_amount / 1000) }}K</span>
              </div>
              <div class="order-item">
                <span class="item-label">支付方式:</span>
                <span class="item-value">{{ getPaymentMethodName(selectedPayment) }}</span>
              </div>
            </div>
            
            <BaseButton
              variant="primary"
              size="lg"
              :loading="paymentLoading"
              @click="createOrder"
              class="pay-button"
            >
              立即支付
            </BaseButton>
          </BaseCard>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user.js'
import { publicAPI } from '@/api/public.js'
import { paymentAPI } from '@/api/payment.js'
import { BaseCard, BaseButton, LoadingSpinner } from '@/components/ui'

const router = useRouter()
const userStore = useUserStore()

const loading = ref(true)
const paymentLoading = ref(false)
const packages = ref([])
const selectedPackage = ref(null)
const selectedPayment = ref('')

// 支付方式配置
const paymentMethods = [
  {
    id: 'alipay',
    name: '支付宝',
    description: '使用支付宝安全支付',
    icon: '💰'
  },
  {
    id: 'wechat_pay',
    name: '微信支付',
    description: '使用微信安全支付',
    icon: '💚'
  }
]

// 计算优惠百分比
const getDiscountPercent = (pkg) => {
  const baseRatio = 1000 // 基础比例：1元 = 1000余额
  const actualRatio = pkg.balance_amount / parseFloat(pkg.price)
  const discount = ((actualRatio - baseRatio) / baseRatio) * 100
  return Math.round(discount)
}

// 获取支付方式名称
const getPaymentMethodName = (methodId) => {
  const method = paymentMethods.find(m => m.id === methodId)
  return method ? method.name : ''
}

// 选择套餐
const selectPackage = (pkg) => {
  selectedPackage.value = pkg
  // 默认选择支付宝
  if (!selectedPayment.value) {
    selectedPayment.value = 'alipay'
  }
}

// 创建订单
const createOrder = async () => {
  if (!selectedPackage.value || !selectedPayment.value) {
    return
  }

  try {
    paymentLoading.value = true
    
    const response = await paymentAPI.createOrder({
      package_id: selectedPackage.value.id,
      payment_method: selectedPayment.value
    })
    
    // 模拟跳转到支付页面
    alert(`订单创建成功！\n订单号: ${response.order_sn}\n请在新窗口中完成支付`)
    
    // 实际项目中这里应该跳转到支付页面或打开支付二维码
    // window.open(response.pay_url, '_blank')
    
    // 模拟支付成功，刷新用户余额
    setTimeout(async () => {
      await userStore.fetchProfile()
      alert('支付成功！余额已更新')
      router.push('/profile')
    }, 2000)
    
  } catch (error) {
    alert('订单创建失败: ' + error.message)
  } finally {
    paymentLoading.value = false
  }
}

// 加载套餐数据
const loadPackages = async () => {
  try {
    loading.value = true
    const response = await publicAPI.getRechargePackages()
    packages.value = response
    
    // 加载用户信息
    await userStore.fetchProfile()
  } catch (error) {
    console.error('Failed to load packages:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadPackages()
})
</script>

<style scoped>
.recharge-page {
  min-height: 100vh;
  padding-top: 80px; /* 为导航栏留出空间 */
  padding-bottom: var(--spacing-3xl);
}

.recharge-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xl);
}

.current-balance {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--color-background-glass);
  border-radius: var(--border-radius-pill);
  border: var(--glass-border);
  backdrop-filter: var(--glass-backdrop);
}

.balance-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.balance-amount {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-accent-yellow);
}

.recharge-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3xl);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

/* 套餐网格 */
.packages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.package-card {
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.package-card.selected {
  border-color: var(--color-accent-cyan);
  box-shadow: var(--shadow-glow-cyan);
  transform: translateY(-4px);
}

.package-content {
  text-align: center;
  position: relative;
}

.package-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
}

.package-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: var(--spacing-sm);
}

.price-symbol {
  font-size: var(--font-size-lg);
  color: var(--color-accent-yellow);
}

.price-amount {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-accent-yellow);
}

.package-balance {
  font-size: var(--font-size-lg);
  color: var(--color-accent-cyan);
  margin-bottom: var(--spacing-md);
  font-weight: var(--font-weight-medium);
}

.package-description {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
  margin-bottom: var(--spacing-md);
}

.discount-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: var(--color-accent-red);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-pill);
  transform: rotate(15deg);
}

/* 支付方式 */
.payment-methods {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-width: 500px;
  margin: 0 auto;
}

.payment-method {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--color-background-glass);
  border: var(--glass-border);
  border-radius: var(--border-radius-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  backdrop-filter: var(--glass-backdrop);
}

.payment-method:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.payment-method.selected {
  border-color: var(--color-accent-cyan);
  box-shadow: var(--shadow-glow-cyan);
}

.method-icon {
  font-size: 2rem;
  width: 60px;
  text-align: center;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.method-desc {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
  margin: 0;
}

/* 订单确认 */
.order-section {
  max-width: 500px;
  margin: 0 auto;
}

.order-summary {
  text-align: center;
}

.order-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-lg);
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.item-label {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
}

.item-value {
  font-size: var(--font-size-base);
  color: var(--color-text-primary);
  font-weight: var(--font-weight-medium);
}

.item-value.price {
  color: var(--color-accent-yellow);
  font-weight: var(--font-weight-bold);
}

.item-value.balance {
  color: var(--color-accent-cyan);
  font-weight: var(--font-weight-bold);
}

.pay-button {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .packages-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .section-title {
    font-size: var(--font-size-xl);
  }

  .payment-method {
    padding: var(--spacing-md);
  }

  .method-icon {
    font-size: 1.5rem;
    width: 50px;
  }
}

@media (max-width: 480px) {
  .recharge-page {
    padding-top: 70px;
  }

  .current-balance {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .package-price {
    flex-direction: column;
    align-items: center;
  }

  .price-amount {
    font-size: var(--font-size-2xl);
  }
}
</style>
