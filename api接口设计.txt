好的，收到您的项目功能清单和数据库设计方案。这是一份非常清晰、全面的需求文档，为我们设计高质量的API架构奠定了坚实的基础。

作为资深后端架构师，我将严格遵循RESTful设计规范，为您打造一套结构清晰、安全可靠、易于扩展的API架构。

-----

### **项目API架构设计文档**

**版本:** 1.0
**架构师:** YZFly

### **一、 总体设计规范**

#### **1.1 根路径与版本控制**

所有API均以根路径 `/api` 开始，并包含版本号。当前版本为 `v1`。

  - **Base URL:** `https://your-domain.com/api/v1`

#### **1.2 认证机制**

  - **认证方式:** 使用 `JSON Web Token (JWT)` 进行无状态认证。
  - **Token传递:** 客户端在登录成功后获取 `access_token`，后续请求需在 `HTTP Header` 中携带：
    ```
    Authorization: Bearer <your_access_token>
    ```
  - **Token载体 (Payload):**
    ```json
    {
      "userId": 1,
      "uid": "u-xxxxxxxx",
      "role": "user", // or "admin"
      "exp": 1678886400 // 过期时间戳
    }
    ```
  - **接口分类:**
      - **公开接口 (Public):** 无需认证，如获取角色列表、注册。
      - **认证接口 (Authenticated):** 需要有效的JWT，供注册用户使用。
      - **管理员接口 (Admin):** 需要有效的JWT，且`role`必须为`admin`。

#### **1.3 统一响应格式**

所有API响应都遵循统一的JSON结构，以便客户端进行统一处理。

  - **成功响应:**
    ```json
    {
      "code": 0,
      "message": "Success",
      "data": { ... } // 业务数据，可以是对象或数组
    }
    ```
  - **失败响应:**
    ```json
    {
      "code": 40001, // 错误码，见下文
      "message": "请求参数错误: 用户名不能为空", // 错误信息
      "data": null
    }
    ```

#### **1.4 HTTP状态码**

  - `200 OK`: 请求成功。
  - `201 Created`: 资源创建成功。
  - `400 Bad Request`: 请求无效（如参数错误）。
  - `401 Unauthorized`: 未认证或Token无效。
  - `403 Forbidden`: 已认证但无权访问。
  - `404 Not Found`: 请求的资源不存在。
  - `422 Unprocessable Entity`: 业务逻辑错误（如余额不足）。
  - `500 Internal Server Error`: 服务器内部错误。

#### **1.5 统一错误码定义**

| Code | HTTP Status | 描述 (Message) |
| :--- | :--- | :--- |
| 0 | 200 | Success |
| **客户端错误 (4xxxx)** |
| 40001 | 400 | Bad Request: 请求参数校验失败 |
| 40101 | 401 | Unauthorized: Token缺失或无效 |
| 40102 | 401 | Unauthorized: 用户名或密码错误 |
| 40301 | 403 | Forbidden: 权限不足，禁止访问 |
| 40401 | 404 | Not Found: 请求的资源不存在 |
| 40901 | 409 | Conflict: 资源冲突（如手机号或邮箱已注册） |
| 42201 | 422 | Unprocessable Entity: 余额不足 |
| **服务端错误 (5xxxx)** |
| 50001 | 500 | Internal Server Error: 服务器内部未知错误 |
| 50002 | 500 | Internal Server Error: 调用第三方AI服务失败 |

-----

### **二、 API 接口详细设计**

按功能模块划分，接口URL使用名词复数形式。

#### **模块一：认证模块 (Authentication)**

  - **路径前缀:** `/api/v1/auth`
  - **认证要求:** 公开

| 功能名称 | HTTP方法 | URL路径 | 参数说明 | 成功响应示例 |
| :--- | :--- | :--- | :--- | :--- |
| **用户注册** | `POST` | `/register` | **Body (JSON):**\<br\>`phone_number` (string, optional)\<br\>`email` (string, optional)\<br\>`password` (string, required)\<br\>`verification_code` (string, if phone used) | `json{  "code": 0,  "message": "Success",  "data": {    "user": {      "uid": "u-d290f1ee",      "email": "<EMAIL>",      "balance": "10.0000"    },    "access_token": "ey..."  }}` |
| **用户登录** | `POST` | `/login` | **Body (JSON):**\<br\>`identity` (string, required, phone or email)\<br\>`password` (string, required) | `json{  "code": 0,  "message": "Success",  "data": {    "access_token": "ey..."  }}` |
| **用户登出** | `POST` | `/logout` | **认证:** User\<br\>无需参数 | `json{  "code": 0,  "message": "Success",  "data": null}` |

-----

#### **模块二：公开访问模块 (Public Access)**

  - **路径前缀:** `/api/v1`
  - **认证要求:** 公开

| 功能名称 | HTTP方法 | URL路径 | 参数说明 | 成功响应示例 |
| :--- | :--- | :--- | :--- | :--- |
| **获取AI角色列表** | `GET` | `/ai-characters` | **Query:**\<br\>`page` (int, optional, def: 1)\<br\>`pageSize` (int, optional, def: 10) | `json{  "code": 0,  "message": "Success",  "data": {    "list": [      {        "id": 1,        "name": "温柔的倾听者",        "avatar_url": "...",        "description": "...",        "popularity": 1234      }    ],    "total": 25  }}` |
| **获取单个AI角色详情** | `GET` | `/ai-characters/{id}` | **Path:**\<br\>`id` (int, required) | `json{  "code": 0,  "message": "Success",  "data": {    "id": 1,    "name": "温柔的倾听者",    "avatar_url": "...",    "description": "...",    "system_prompt": "你是一个...",    "popularity": 1234  }}` |
| **获取充值套餐列表** | `GET` | `/recharge-packages` | 无 | `json{  "code": 0,  "message": "Success",  "data": [    {      "id": 1,      "name": "尝鲜包",      "price": "6.00",      "balance_amount": "6000.0000"    }  ]}` |

-----

#### **模块三：核心对话模块 (Chat)**

  - **路径前缀:** `/api/v1/chat`
  - **认证要求:** 注册用户 (User)

| 功能名称 | HTTP方法 | URL路径 | 参数说明 | 成功响应示例 |
| :--- | :--- | :--- | :--- | :--- |
| **发送消息并获取AI回复** | `POST` | `/messages` | **Body (JSON):**\<br\>`character_id` (int, required)\<br\>`content` (string, required)\<br\>`stream` (boolean, optional, def: false) - *是否流式返回* | `json// 非流式响应{  "code": 0,  "message": "Success",  "data": {    "reply_message": {      "sender_type": "ai",      "content": "我完全理解你的感受。"    },    "balance_change": "-2.5000",    "current_balance": "87.5000"  }}` |
| **获取历史聊天记录** | `GET` | `/sessions/{character_id}/messages` | **Path:**\<br\>`character_id` (int, required)\<br\>\<br\>**Query:**\<br\>`last_message_id` (long, optional) - *用于分页加载*\<br\>`limit` (int, optional, def: 20) | `json{  "code": 0,  "message": "Success",  "data": {    "messages": [      { "id": 102, "sender_type": "ai", "content": "你好", "created_at": "..." },      { "id": 101, "sender_type": "user", "content": "在吗", "created_at": "..." }    ]  }}` |
| **获取会话列表** | `GET` | `/sessions` | **Query:**\<br\>`page` (int, optional, def: 1)\<br\>`pageSize` (int, optional, def: 10) | `json{  "code": 0,  "message": "Success",  "data": {    "list": [      {        "character": { "id": 1, "name": "...", "avatar_url": "..." },        "last_message": "好的，我们下次再聊",        "updated_at": "..."      }    ],    "total": 5  }}` |

**注意:** 发送消息接口 (`POST /chat/messages`) 是核心中的核心。后端逻辑：

1.  验证用户余额是否充足。
2.  查找或创建 `chat_sessions`。
3.  将用户消息存入 `chat_messages`。
4.  （高级功能）提取长期记忆并注入到 Prompt。
5.  构建完整的 Prompt（包含系统设定、历史对话、用户新消息），调用AI服务。
6.  接收AI回复，记录 `prompt_tokens` 和 `completion_tokens`。
7.  根据计费规则计算消耗的余额。
8.  **在单个数据库事务中**:
    a. 扣除用户 `balance`。
    b. 将AI回复存入 `chat_messages`。
    c. 记录一条 `consumption_logs`。
9.  返回响应给客户端。

-----

#### **模块四：用户中心模块 (User Center)**

  - **路径前缀:** `/api/v1/user`
  - **认证要求:** 注册用户 (User)

| 功能名称 | HTTP方法 | URL路径 | 参数说明 | 成功响应示例 |
| :--- | :--- | :--- | :--- | :--- |
| **获取当前用户信息** | `GET` | `/profile` | 无 | `json{  "code": 0,  "message": "Success",  "data": {    "uid": "u-d290f1ee",    "phone_number": "138****1234",    "email": "u***@example.com",    "balance": "87.5000",    "created_at": "..."  }}` |
| **修改密码** | `PUT` | `/password` | **Body (JSON):**\<br\>`current_password` (string, required)\<br\>`new_password` (string, required) | `json{  "code": 0,  "message": "Success",  "data": null}` |
| **获取消费记录** | `GET` | `/consumption-logs` | **Query:**\<br\>`page` (int, optional, def: 1)\<br\>`pageSize` (int, optional, def: 10) | `json{  "code": 0,  "message": "Success",  "data": {    "list": [      {        "description": "与'温柔倾听者'的对话",        "balance_change": "-2.5000",        "balance_after": "87.5000",        "created_at": "..."      }    ],    "total": 50  }}` |
| **获取充值订单记录** | `GET` | `/recharge-orders` | **Query:**\<br\>`page` (int, optional, def: 1)\<br\>`pageSize` (int, optional, def: 10) | `json{  "code": 0,  "message": "Success",  "data": {    "list": [      {        "order_sn": "...",        "amount_paid": "30.00",        "balance_granted": "35000.0000",        "status": "completed",        "created_at": "..."      }    ],    "total": 3  }}` |

-----

#### **模块五：支付模块 (Payment)**

  - **路径前缀:** `/api/v1`
  - **认证要求:** 注册用户 (User) / 公开 (回调)

| 功能名称 | HTTP方法 | URL路径 | 参数说明 | 成功响应示例 |
| :--- | :--- | :--- | :--- | :--- |
| **创建充值订单** | `POST` | `/orders` | **认证:** User\<br\>\<br\>**Body (JSON):**\<br\>`package_id` (int, required)\<br\>`payment_method` (string, req, e.g. `alipay`, `wechat_pay`) | `json{  "code": 0,  "message": "Success",  "data": {    "order_sn": "RO20250721...",    "pay_url": "aliapy://..."  }}` |
| **查询订单状态** | `GET` | `/orders/{order_sn}` | **认证:** User\<br\>\<br\>**Path:**\<br\>`order_sn` (string, required) | `json{  "code": 0,  "message": "Success",  "data": {    "order_sn": "...",    "status": "completed"  }}` |
| **支付回调通知** | `POST` | `/payment/notify/{payment_method}` | **认证:** 公开 (IP白名单+签名验证)\<br\>\<br\>**Path:**\<br\>`payment_method` (string, required)\<br\>\<br\>**Body:** 支付宝/微信支付的异步通知数据 | `success` 或 `fail` (纯文本，按支付平台要求) |

-----

#### **模块六：后台管理模块 (Admin Panel)**

  - **路径前缀:** `/api/v1/admin`
  - **认证要求:** 管理员 (Admin)

| 功能名称 | HTTP方法 | URL路径 | 参数说明 | 成功响应示例 |
| :--- | :--- | :--- | :--- | :--- |
| **获取仪表盘数据** | `GET` | `/dashboard/stats` | 无 | `json{ "code": 0, "data": { "new_users": 10, "active_users": 150, "total_revenue": "1234.56" } }` |
| **获取用户列表** | `GET` | `/users` | **Query:** `search`, `page`, `pageSize` | `...` |
| **管理单个用户** | `PUT` | `/users/{id}` | **Body:** `balance` (decimal), `status` (enum) | `...` |
| **创建AI角色** | `POST` | `/ai-characters` | **Body:** `name`, `avatar_url`, `description`, `system_prompt`, ... | `... (返回新创建的角色信息)` |
| **更新AI角色** | `PUT` | `/ai-characters/{id}` | **Body:** `name`, `avatar_url`, `description`, `system_prompt`, ... | `...` |
| **删除AI角色** | `DELETE`| `/ai-characters/{id}` | 无 | `... (返回空data)` |
| **获取财务订单列表** | `GET` | `/recharge-orders` | **Query:** `date_range`, `status`, ... | `...` |
| **获取系统配置** | `GET` | `/system-configs` | 无 | `json{ "code": 0, "data": { "token_to_balance_ratio": "1", "new_user_bonus": "100" } }` |
| **更新系统配置** | `PUT` | `/system-configs` | **Body:** `{ "config_key": "...", "config_value": "..." }` | `...` |

-----

### **三、 API 使用说明与最佳实践**

1.  **善用 Query 参数:** 对于列表查询接口，充分利用 `page`, `pageSize`, `sort`, `filter` 等参数实现灵活的数据筛选和分页。
2.  **安全性:**
      - 所有写操作 (POST, PUT, DELETE) 都应考虑防范 `CSRF` 攻击，虽然JWT机制能一定程度缓解，但最佳实践依然重要。
      - 对用户输入进行严格的XSS过滤和SQL注入防范。
      - 后台管理API必须有严格的IP白名单或二次验证机制。
3.  **性能:**
      - 对于高频读取的数据（如AI角色列表、系统配置），应添加缓存层（如 Redis）以降低数据库压力。
      - `chat_messages` 表会是增长最快的表，需要考虑定期归档或分区策略。
4.  **可扩展性:**
      - 当前设计已为“长期记忆”、“动态事件”等亮点功能预留了空间。例如，可以在 `POST /chat/messages` 的逻辑中增加与向量数据库交互的步骤。
      - `system_configs` 表提供了动态调整业务参数的能力，无需修改代码和重新部署。

这套API架构为您的项目提供了一个稳固的后端服务框架。随着业务的发展，我们可以平滑地进行 `v2` 版本的迭代。