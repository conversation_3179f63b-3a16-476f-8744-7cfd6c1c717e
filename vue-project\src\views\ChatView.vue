<template>
  <div class="chat-page">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="container">
        <div class="chat-header-content">
          <div class="character-info">
            <BaseAvatar
              v-if="currentCharacter"
              :src="currentCharacter.avatar_url"
              :name="currentCharacter.name"
              size="md"
              class="character-avatar"
            />
            <div class="character-details">
              <h2 class="character-name">
                {{ currentCharacter?.name || '加载中...' }}
              </h2>
              <p class="character-status">在线</p>
            </div>
          </div>
          
          <div class="chat-actions">
            <div class="balance-display">
              <span class="balance-label">余额:</span>
              <span class="balance-amount">{{ userStore.balance }}</span>
            </div>
            <BaseButton variant="ghost" size="sm" @click="goBack">
              返回
            </BaseButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="chat-messages" ref="messagesContainer">
      <div class="container">
        <LoadingSpinner v-if="loading" center text="加载历史消息..." />
        
        <div v-else class="messages-list">
          <div
            v-for="message in chatStore.messages"
            :key="message.id"
            :class="['message', `message-${message.sender_type}`]"
          >
            <BaseAvatar
              v-if="message.sender_type === 'ai'"
              :src="currentCharacter?.avatar_url"
              :name="currentCharacter?.name"
              size="sm"
              class="message-avatar"
            />
            
            <div class="message-content">
              <div :class="['message-bubble', message.sender_type]">
                {{ message.content }}
              </div>
              <div class="message-time">
                {{ formatTime(message.created_at) }}
              </div>
            </div>
            
            <BaseAvatar
              v-if="message.sender_type === 'user'"
              :src="authStore.user?.avatar"
              :name="authStore.user?.email"
              size="sm"
              class="message-avatar"
            />
          </div>
          
          <!-- AI正在输入指示器 -->
          <div v-if="chatStore.isLoading" class="message message-ai">
            <BaseAvatar
              :src="currentCharacter?.avatar_url"
              :name="currentCharacter?.name"
              size="sm"
              class="message-avatar"
            />
            <div class="message-content">
              <TypingIndicator />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="container">
        <form @submit.prevent="sendMessage" class="input-form">
          <div class="input-wrapper">
            <BaseInput
              v-model="messageText"
              placeholder="输入你想说的话..."
              :disabled="chatStore.isLoading || !canSendMessage"
              @keydown.enter.prevent="sendMessage"
              class="message-input"
            />
            <BaseButton
              type="submit"
              variant="primary"
              :disabled="!messageText.trim() || chatStore.isLoading || !canSendMessage"
              :loading="chatStore.isLoading"
              class="send-button"
            >
              发送
            </BaseButton>
          </div>
          
          <div v-if="!canSendMessage" class="input-warning">
            余额不足，请先充值
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter.js'
import { useChatStore } from '@/stores/chat.js'
import { useUserStore } from '@/stores/user.js'
import { publicAPI } from '@/api/public.js'
import { BaseButton, BaseAvatar, BaseInput, LoadingSpinner, TypingIndicator } from '@/components/ui'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const chatStore = useChatStore()
const userStore = useUserStore()

const messagesContainer = ref(null)
const messageText = ref('')
const loading = ref(true)
const currentCharacter = ref(null)

// 计算是否可以发送消息（余额检查）
const canSendMessage = computed(() => {
  const balance = parseFloat(userStore.balance || '0')
  return balance > 0
})

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 加载角色信息和历史消息
const loadChatData = async () => {
  const characterId = route.params.characterId
  
  try {
    loading.value = true
    
    // 加载角色信息
    const character = await publicAPI.getAICharacter(characterId)
    currentCharacter.value = character
    chatStore.setCurrentCharacter(character)
    
    // 加载历史消息
    await chatStore.loadMessages(characterId)
    
    // 加载用户信息（获取余额）
    await userStore.fetchProfile()
    
    scrollToBottom()
  } catch (error) {
    console.error('Failed to load chat data:', error)
    // 如果角色不存在，返回角色列表
    router.push('/characters')
  } finally {
    loading.value = false
  }
}

// 发送消息
const sendMessage = async () => {
  if (!messageText.value.trim() || chatStore.isLoading || !canSendMessage.value) {
    return
  }

  const content = messageText.value.trim()
  messageText.value = ''

  try {
    const response = await chatStore.sendMessage(content)
    
    // 更新余额
    if (response.current_balance) {
      userStore.updateBalance(response.current_balance)
    }
    
    scrollToBottom()
  } catch (error) {
    console.error('Failed to send message:', error)
    // 恢复输入内容
    messageText.value = content
  }
}

// 返回
const goBack = () => {
  router.push('/characters')
}

// 监听消息变化，自动滚动
watch(() => chatStore.messages.length, () => {
  scrollToBottom()
})

onMounted(() => {
  loadChatData()
})
</script>

<style scoped>
.chat-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 聊天头部 */
.chat-header {
  position: fixed;
  top: 60px; /* 导航栏高度 */
  left: 0;
  right: 0;
  z-index: var(--z-index-dropdown);
  background: var(--color-background-glass);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: var(--glass-border);
  padding: var(--spacing-md) 0;
}

.chat-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.character-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.character-details {
  display: flex;
  flex-direction: column;
}

.character-name {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
}

.character-status {
  font-size: var(--font-size-sm);
  color: var(--color-accent-green);
  margin: 0;
}

.chat-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.balance-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-background-glass);
  border-radius: var(--border-radius-pill);
  border: var(--glass-border);
}

.balance-label {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

.balance-amount {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-accent-yellow);
}

/* 消息列表 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 140px 0 100px; /* 为头部和输入区域留出空间 */
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) 0;
}

.message {
  display: flex;
  align-items: flex-end;
  gap: var(--spacing-sm);
}

.message-ai {
  justify-content: flex-start;
}

.message-user {
  justify-content: flex-end;
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-user .message-content {
  align-items: flex-end;
}

.message-ai .message-content {
  align-items: flex-start;
}

.message-time {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  margin-top: var(--spacing-xs);
  padding: 0 var(--spacing-sm);
}

/* 输入区域 */
.chat-input {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-dropdown);
  background: var(--color-background-glass);
  backdrop-filter: var(--glass-backdrop);
  border-top: var(--glass-border);
  padding: var(--spacing-md) 0;
}

.input-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.input-wrapper {
  display: flex;
  gap: var(--spacing-sm);
  align-items: flex-end;
}

.message-input {
  flex: 1;
}

.send-button {
  flex-shrink: 0;
}

.input-warning {
  font-size: var(--font-size-sm);
  color: var(--color-accent-red);
  text-align: center;
  padding: var(--spacing-xs);
  background: rgba(239, 68, 68, 0.1);
  border-radius: var(--border-radius-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header-content {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
  }

  .chat-actions {
    width: 100%;
    justify-content: space-between;
  }

  .character-name {
    font-size: var(--font-size-base);
  }

  .message-content {
    max-width: 85%;
  }

  .input-wrapper {
    flex-direction: column;
    align-items: stretch;
  }

  .send-button {
    align-self: flex-end;
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .chat-messages {
    padding: 120px 0 120px;
  }

  .balance-display {
    font-size: var(--font-size-xs);
  }
}
</style>
