<template>
  <div class="characters-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="container">
        <h1 class="page-title">AI 伙伴</h1>
        <p class="page-subtitle">选择最适合你的倾听者</p>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="characters-content">
      <div class="container">
        <LoadingSpinner v-if="loading" center text="加载角色中..." />
        
        <div v-else>
          <div class="characters-grid">
            <BaseCard
              v-for="character in characters"
              :key="character.id"
              :hoverable="true"
              :clickable="true"
              class="character-card"
              @click="selectCharacter(character)"
            >
              <div class="character-content">
                <BaseAvatar
                  :src="character.avatar_url"
                  :name="character.name"
                  :alt="character.name"
                  size="xl"
                  class="character-avatar"
                />
                <div class="character-info">
                  <h3 class="character-name">{{ character.name }}</h3>
                  <p class="character-description">{{ character.description }}</p>
                  <div class="character-stats">
                    <span class="popularity">{{ character.popularity }} 人在聊</span>
                  </div>
                </div>
                <div class="character-actions">
                  <BaseButton 
                    variant="primary" 
                    size="sm"
                    @click.stop="startChat(character)"
                  >
                    开始聊天
                  </BaseButton>
                  <BaseButton 
                    variant="ghost" 
                    size="sm"
                    @click.stop="viewDetails(character)"
                  >
                    查看详情
                  </BaseButton>
                </div>
              </div>
            </BaseCard>
          </div>

          <!-- 分页 -->
          <div v-if="total > pageSize" class="pagination">
            <BaseButton
              variant="ghost"
              :disabled="currentPage === 1"
              @click="loadPage(currentPage - 1)"
            >
              上一页
            </BaseButton>
            <span class="page-info">
              第 {{ currentPage }} 页，共 {{ totalPages }} 页
            </span>
            <BaseButton
              variant="ghost"
              :disabled="currentPage === totalPages"
              @click="loadPage(currentPage + 1)"
            >
              下一页
            </BaseButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter.js'
import { publicAPI } from '@/api/public.js'
import { BaseButton, BaseCard, BaseAvatar, LoadingSpinner } from '@/components/ui'

const router = useRouter()
const authStore = useAuthStore()

const characters = ref([])
const loading = ref(true)
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

// 加载角色列表
const loadCharacters = async (page = 1) => {
  try {
    loading.value = true
    const response = await publicAPI.getAICharacters({
      page,
      pageSize: pageSize.value
    })
    characters.value = response.list
    total.value = response.total
    currentPage.value = page
  } catch (error) {
    console.error('Failed to load characters:', error)
  } finally {
    loading.value = false
  }
}

// 加载指定页面
const loadPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    loadCharacters(page)
  }
}

// 选择角色
const selectCharacter = (character) => {
  // 可以添加角色选择的逻辑
  console.log('Selected character:', character)
}

// 开始聊天
const startChat = (character) => {
  if (authStore.isLoggedIn) {
    router.push(`/chat/${character.id}`)
  } else {
    router.push('/login')
  }
}

// 查看详情
const viewDetails = (character) => {
  // 暂时跳转到聊天页面，后续可以实现详情页
  startChat(character)
}

onMounted(() => {
  loadCharacters()
})
</script>

<style scoped>
.characters-page {
  min-height: 100vh;
  padding-top: 80px; /* 为导航栏留出空间 */
}

.page-header {
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
  text-align: center;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-md);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-text-secondary);
}

.characters-content {
  padding-bottom: var(--spacing-3xl);
}

.characters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.character-card {
  cursor: pointer;
  transition: all var(--transition-normal);
}

.character-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  height: 100%;
}

.character-avatar {
  margin-bottom: var(--spacing-lg);
}

.character-info {
  flex: 1;
  margin-bottom: var(--spacing-lg);
}

.character-name {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-sm);
}

.character-description {
  font-size: var(--font-size-base);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin-bottom: var(--spacing-md);
}

.character-stats {
  display: flex;
  justify-content: center;
}

.popularity {
  font-size: var(--font-size-sm);
  color: var(--color-accent-cyan);
  background: rgba(0, 212, 255, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-pill);
}

.character-actions {
  display: flex;
  gap: var(--spacing-sm);
  width: 100%;
}

.character-actions .btn {
  flex: 1;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.page-info {
  color: var(--color-text-secondary);
  font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
  .characters-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  
  .page-title {
    font-size: var(--font-size-2xl);
  }
  
  .pagination {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}
</style>
