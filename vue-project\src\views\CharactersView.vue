<template>
  <div class="characters-app">
    <!-- APP头部 - 返回按钮和标题 -->
    <div class="app-header">
      <BaseButton variant="ghost" @click="$router.go(-1)" class="back-button">
        ← 返回
      </BaseButton>
      <h1 class="app-title">AI 伙伴</h1>
    </div>

    <!-- APP内容区域 - 可滚动的角色列表 -->
    <div class="app-content">
      <LoadingSpinner v-if="loading" center text="加载角色中..." />

      <div v-else class="characters-list">
        <div
          v-for="character in characters"
          :key="character.id"
          class="character-item glass-card"
          @click="startChat(character)"
        >
          <BaseAvatar
            :src="character.avatar_url"
            :name="character.name"
            :alt="character.name"
            size="lg"
            class="character-avatar"
          />
          <div class="character-info">
            <h3 class="character-name">{{ character.name }}</h3>
            <p class="character-description">{{ character.description }}</p>
            <span class="character-popularity">{{ character.popularity }} 人在聊</span>
          </div>
          <div class="character-arrow">→</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/counter.js'
import { publicAPI } from '@/api/public.js'
import { BaseButton, BaseAvatar, LoadingSpinner } from '@/components/ui'

const router = useRouter()
const authStore = useAuthStore()

const characters = ref([])
const loading = ref(true)
const currentPage = ref(1)
const pageSize = ref(12)
const total = ref(0)

const totalPages = computed(() => Math.ceil(total.value / pageSize.value))

// 加载角色列表
const loadCharacters = async (page = 1) => {
  try {
    loading.value = true
    const response = await publicAPI.getAICharacters({
      page,
      pageSize: pageSize.value
    })
    characters.value = response.list
    total.value = response.total
    currentPage.value = page
  } catch (error) {
    console.error('Failed to load characters:', error)
  } finally {
    loading.value = false
  }
}

// 加载指定页面
const loadPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    loadCharacters(page)
  }
}

// 选择角色
const selectCharacter = (character) => {
  // 可以添加角色选择的逻辑
  console.log('Selected character:', character)
}

// 开始聊天
const startChat = (character) => {
  if (authStore.isLoggedIn) {
    router.push(`/chat/${character.id}`)
  } else {
    router.push('/login')
  }
}

// 查看详情
const viewDetails = (character) => {
  // 暂时跳转到聊天页面，后续可以实现详情页
  startChat(character)
}

onMounted(() => {
  loadCharacters()
})
</script>

<style scoped>
/* 角色选择APP页面 - 全屏视口，列表形式 */
.characters-app {
  height: 100vh;
  width: 100vw;
  background: var(--color-background-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* APP头部 - 固定在顶部 */
.app-header {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-md);
  background: var(--color-glass-bg);
  backdrop-filter: var(--blur-glass);
  border-bottom: 1px solid var(--color-glass-border);
}

.back-button {
  margin-right: var(--spacing-md);
  min-width: auto;
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-small);
}

.app-title {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0;
  background: linear-gradient(135deg, var(--color-accent-cyan) 0%, var(--color-accent-yellow) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* APP内容区域 - 可滚动 */
.app-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-md);
}

/* 角色列表 - 垂直列表布局 */
.characters-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.character-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg);
  cursor: pointer;
  transition: all var(--transition-normal);
  border-radius: var(--border-radius-md);
}

.character-item:active {
  transform: scale(0.98);
  background: var(--color-glass-bg-hover);
}

.character-avatar {
  flex: 0 0 auto;
  margin-right: var(--spacing-lg);
}

.character-info {
  flex: 1;
  text-align: left;
}

.character-name {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-xs) 0;
}

.character-description {
  font-size: var(--font-size-small);
  color: var(--color-text-secondary);
  line-height: var(--line-height-normal);
  margin: 0 0 var(--spacing-xs) 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.character-popularity {
  font-size: var(--font-size-caption);
  color: var(--color-accent-cyan);
  background: rgba(0, 255, 255, 0.1);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-pill);
  display: inline-block;
}

.character-arrow {
  flex: 0 0 auto;
  font-size: var(--font-size-h3);
  color: var(--color-text-muted);
  margin-left: var(--spacing-md);
}

/* 移动端优化 */
@media (max-width: 480px) {
  .app-header {
    padding: var(--spacing-md) var(--spacing-sm);
  }

  .app-content {
    padding: var(--spacing-sm);
  }

  .character-item {
    padding: var(--spacing-md);
  }

  .character-avatar {
    margin-right: var(--spacing-md);
  }

  .character-name {
    font-size: var(--font-size-body);
  }

  .character-description {
    font-size: var(--font-size-caption);
  }
}

/* 滚动条样式 */
.app-content::-webkit-scrollbar {
  width: 2px;
}

.app-content::-webkit-scrollbar-track {
  background: transparent;
}

.app-content::-webkit-scrollbar-thumb {
  background: var(--color-accent-cyan);
  border-radius: var(--border-radius-pill);
}
</style>
