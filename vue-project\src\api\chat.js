// 聊天相关 API
import api, { USE_MOCK, createMockWrapper, mockAPI } from './index.js'

export const chatAPI = {
  // 发送消息并获取AI回复
  sendMessage: USE_MOCK
    ? createMockWrapper(mockAPI.chat.sendMessage)
    : (data) => api.post('/chat/messages', data),

  // 获取历史聊天记录
  getMessages: USE_MOCK
    ? createMockWrapper(mockAPI.chat.getMessages)
    : (characterId, params = {}) => api.get(`/chat/sessions/${characterId}/messages`, { params }),

  // 获取会话列表
  getSessions: USE_MOCK
    ? createMockWrapper(mockAPI.chat.getSessions)
    : (params = {}) => api.get('/chat/sessions', { params })
}
