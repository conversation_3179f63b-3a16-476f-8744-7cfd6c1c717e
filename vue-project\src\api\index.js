// API 服务层 - 统一管理所有接口调用
import axios from 'axios'
import { mockAPI } from '@/mock/index.js'

// 是否使用 Mock 数据 (开发环境默认使用)
const USE_MOCK = import.meta.env.DEV || import.meta.env.VITE_USE_MOCK === 'true'

// 创建 axios 实例
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加 JWT token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一处理响应
api.interceptors.response.use(
  (response) => {
    const { data } = response
    if (data.code === 0) {
      return data.data
    } else {
      // 处理业务错误
      throw new Error(data.message || '请求失败')
    }
  },
  (error) => {
    // 处理 HTTP 错误
    if (error.response?.status === 401) {
      // Token 过期，清除本地存储并跳转登录
      localStorage.removeItem('access_token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Mock 模式下的 API 调用包装器
const createMockWrapper = (mockFn) => {
  return async (...args) => {
    if (USE_MOCK) {
      try {
        const response = await mockFn(...args)
        return response.data
      } catch (error) {
        throw error
      }
    } else {
      // 实际 API 调用逻辑会在这里
      throw new Error('Real API not implemented yet')
    }
  }
}

export default api
export { USE_MOCK, createMockWrapper, mockAPI }
