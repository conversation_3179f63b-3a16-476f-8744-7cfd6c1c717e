<template>
  <nav class="navbar">
    <div class="container">
      <div class="navbar-content">
        <!-- 品牌/Logo -->
        <div class="navbar-brand">
          <router-link to="/" class="navbar-brand-link">
            <slot name="brand">
              {{ brandText }}
            </slot>
          </router-link>
        </div>
        
        <!-- 导航菜单 -->
        <div class="navbar-nav">
          <slot name="nav" />
        </div>
        
        <!-- 用户操作区 -->
        <div class="navbar-actions">
          <slot name="actions" />
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
const props = defineProps({
  brandText: {
    type: String,
    default: '温暖树洞'
  }
})
</script>

<style scoped>
.navbar-brand-link {
  color: var(--color-text-primary);
  text-decoration: none;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

@media (max-width: 768px) {
  .navbar-nav {
    display: none;
  }
  
  .navbar-brand-link {
    font-size: var(--font-size-lg);
  }
}
</style>
