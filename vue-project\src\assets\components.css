/* 组件样式系统 */

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--border-radius-pill);
  font-family: inherit;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  user-select: none;
  white-space: nowrap;
  min-height: 44px; /* 移动端友好的触摸目标 */
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 主要按钮 - 黄色 */
.btn-primary {
  background: var(--color-accent-yellow);
  color: var(--color-primary-dark);
  box-shadow: var(--shadow-glow-yellow);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow-yellow), var(--shadow-lg);
}

.btn-primary:active {
  transform: translateY(0);
}

/* 次要按钮 - 青色 */
.btn-secondary {
  background: var(--color-accent-cyan);
  color: var(--color-primary-dark);
  box-shadow: var(--shadow-glow-cyan);
}

.btn-secondary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-glow-cyan), var(--shadow-lg);
}

/* 幽灵按钮 */
.btn-ghost {
  background: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-accent-cyan);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--color-accent-cyan);
  color: var(--color-primary-dark);
  box-shadow: var(--shadow-glow-cyan);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--spacing-xs) var(--spacing-md);
  font-size: var(--font-size-sm);
  min-height: 36px;
}

.btn-lg {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--font-size-lg);
  min-height: 52px;
}

/* 输入框组件 */
.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--color-background-glass);
  border: var(--glass-border);
  border-radius: var(--border-radius-md);
  color: var(--color-text-primary);
  font-family: inherit;
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  backdrop-filter: var(--glass-backdrop);
  min-height: 44px;
}

.input::placeholder {
  color: var(--color-text-muted);
}

.input:focus {
  outline: none;
  border-color: var(--color-accent-cyan);
  box-shadow: var(--shadow-glow-cyan);
}

.input:invalid {
  border-color: var(--color-accent-red);
}

/* 卡片组件 */
.card {
  background: var(--color-background-glass);
  backdrop-filter: var(--glass-backdrop);
  border: var(--glass-border);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  margin-bottom: var(--spacing-xs);
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-text-secondary);
}

/* 头像组件 */
.avatar {
  display: inline-block;
  border-radius: 50%;
  overflow: hidden;
  background: var(--gradient-primary);
  border: 2px solid var(--color-accent-cyan);
}

.avatar-sm {
  width: 32px;
  height: 32px;
}

.avatar-md {
  width: 48px;
  height: 48px;
}

.avatar-lg {
  width: 64px;
  height: 64px;
}

.avatar-xl {
  width: 96px;
  height: 96px;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 徽章组件 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--color-accent-cyan);
  color: var(--color-primary-dark);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--border-radius-pill);
}

.badge-yellow {
  background: var(--color-accent-yellow);
}

.badge-green {
  background: var(--color-accent-green);
}

.badge-red {
  background: var(--color-accent-red);
}

/* 导航栏组件 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-dropdown);
  background: var(--color-background-glass);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: var(--glass-border);
  padding: var(--spacing-sm) 0;
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.navbar-brand {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.navbar-item {
  color: var(--color-text-secondary);
  text-decoration: none;
  transition: color var(--transition-normal);
}

.navbar-item:hover,
.navbar-item.active {
  color: var(--color-accent-cyan);
}

/* 消息气泡组件 */
.message-bubble {
  max-width: 80%;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-sm);
  word-wrap: break-word;
}

.message-bubble.user {
  background: var(--color-accent-cyan);
  color: var(--color-primary-dark);
  margin-left: auto;
  border-bottom-right-radius: var(--border-radius-sm);
}

.message-bubble.ai {
  background: var(--color-background-glass);
  color: var(--color-text-primary);
  border: var(--glass-border);
  backdrop-filter: var(--glass-backdrop);
  border-bottom-left-radius: var(--border-radius-sm);
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-text-muted);
  border-radius: 50%;
  border-top-color: var(--color-accent-cyan);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 打字动画 */
.typing {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.typing-dot {
  width: 6px;
  height: 6px;
  background: var(--color-accent-cyan);
  border-radius: 50%;
  animation: typing 1.4s ease-in-out infinite both;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
