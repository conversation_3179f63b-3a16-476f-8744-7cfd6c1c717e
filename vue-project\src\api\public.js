// 公开访问 API
import api, { USE_MOCK, createMockWrapper, mockAPI } from './index.js'

export const publicAPI = {
  // 获取AI角色列表
  getAICharacters: USE_MOCK
    ? createMockWrapper(mockAPI.public.getAICharacters)
    : (params = {}) => api.get('/ai-characters', { params }),

  // 获取单个AI角色详情
  getAICharacter: USE_MOCK
    ? createMockWrapper(mockAPI.public.getAICharacter)
    : (id) => api.get(`/ai-characters/${id}`),

  // 获取充值套餐列表
  getRechargePackages: USE_MOCK
    ? createMockWrapper(mockAPI.public.getRechargePackages)
    : () => api.get('/recharge-packages')
}
