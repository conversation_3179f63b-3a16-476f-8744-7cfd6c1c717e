// 用户数据状态管理
import { ref } from 'vue'
import { defineStore } from 'pinia'
import { userAPI } from '@/api/user.js'

export const useUserStore = defineStore('user', () => {
  const profile = ref(null)
  const balance = ref('0.0000')
  const consumptionLogs = ref([])
  const rechargeOrders = ref([])
  
  // 获取用户资料
  async function fetchProfile() {
    try {
      const response = await userAPI.getProfile()
      profile.value = response
      balance.value = response.balance
      return response
    } catch (error) {
      throw error
    }
  }
  
  // 更新余额
  function updateBalance(newBalance) {
    balance.value = newBalance
    if (profile.value) {
      profile.value.balance = newBalance
    }
  }
  
  // 获取消费记录
  async function fetchConsumptionLogs(params = {}) {
    try {
      const response = await userAPI.getConsumptionLogs(params)
      if (params.page === 1) {
        consumptionLogs.value = response.list
      } else {
        consumptionLogs.value.push(...response.list)
      }
      return response
    } catch (error) {
      throw error
    }
  }
  
  // 获取充值记录
  async function fetchRechargeOrders(params = {}) {
    try {
      const response = await userAPI.getRechargeOrders(params)
      if (params.page === 1) {
        rechargeOrders.value = response.list
      } else {
        rechargeOrders.value.push(...response.list)
      }
      return response
    } catch (error) {
      throw error
    }
  }
  
  // 修改密码
  async function changePassword(data) {
    try {
      const response = await userAPI.changePassword(data)
      return response
    } catch (error) {
      throw error
    }
  }

  return {
    profile,
    balance,
    consumptionLogs,
    rechargeOrders,
    fetchProfile,
    updateBalance,
    fetchConsumptionLogs,
    fetchRechargeOrders,
    changePassword
  }
})
