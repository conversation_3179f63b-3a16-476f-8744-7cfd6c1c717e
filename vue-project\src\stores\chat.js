// 聊天状态管理
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { chatAPI } from '@/api/chat.js'

export const useChatStore = defineStore('chat', () => {
  const currentCharacter = ref(null)
  const messages = ref([])
  const sessions = ref([])
  const isLoading = ref(false)
  
  // 设置当前对话角色
  function setCurrentCharacter(character) {
    currentCharacter.value = character
    messages.value = [] // 清空消息列表
  }
  
  // 发送消息
  async function sendMessage(content) {
    if (!currentCharacter.value) return
    
    // 添加用户消息到列表
    const userMessage = {
      id: Date.now(),
      sender_type: 'user',
      content,
      created_at: new Date().toISOString()
    }
    messages.value.push(userMessage)
    
    try {
      isLoading.value = true
      const response = await chatAPI.sendMessage({
        character_id: currentCharacter.value.id,
        content,
        stream: false
      })
      
      // 添加AI回复到列表
      const aiMessage = {
        id: Date.now() + 1,
        sender_type: 'ai',
        content: response.reply_message.content,
        created_at: new Date().toISOString()
      }
      messages.value.push(aiMessage)
      
      return response
    } catch (error) {
      // 发送失败，移除用户消息
      messages.value.pop()
      throw error
    } finally {
      isLoading.value = false
    }
  }
  
  // 加载历史消息
  async function loadMessages(characterId, params = {}) {
    try {
      const response = await chatAPI.getMessages(characterId, params)
      if (params.last_message_id) {
        // 分页加载，追加到现有消息前面
        messages.value.unshift(...response.messages.reverse())
      } else {
        // 首次加载
        messages.value = response.messages.reverse()
      }
      return response
    } catch (error) {
      throw error
    }
  }
  
  // 加载会话列表
  async function loadSessions(params = {}) {
    try {
      const response = await chatAPI.getSessions(params)
      sessions.value = response.list
      return response
    } catch (error) {
      throw error
    }
  }
  
  // 清空当前对话
  function clearCurrentChat() {
    currentCharacter.value = null
    messages.value = []
  }

  return {
    currentCharacter,
    messages,
    sessions,
    isLoading,
    setCurrentCharacter,
    sendMessage,
    loadMessages,
    loadSessions,
    clearCurrentChat
  }
})
